# WowPic项目MySQL常用语句

## 📋 目录
- [删除生成失败的数据](#删除生成失败的数据)
- [数据查询统计](#数据查询统计)
- [用户管理](#用户管理)
- [图片生成管理](#图片生成管理)
- [数据库维护](#数据库维护)
- [性能优化](#性能优化)

---

## 🗑️ 删除生成失败的数据

### 删除失败的生成记录（包括关联的图片）
```sql
-- 删除状态为FAILED的生成记录（会自动删除关联的图片，因为有CASCADE）
DELETE FROM generations 
WHERE status = 'FAILED';

-- 删除指定时间之前的失败记录
DELETE FROM generations 
WHERE status = 'FAILED' 
AND created_at < DATE_SUB(NOW(), INTERVAL 7 DAY);

-- 删除特定用户的失败记录
DELETE FROM generations 
WHERE status = 'FAILED' 
AND user_id = 123;
```

### 删除孤儿图片数据
```sql
-- 删除没有对应生成记录的图片
DELETE gi FROM generation_images gi
LEFT JOIN generations g ON gi.generation_id = g.id
WHERE g.id IS NULL;

-- 删除没有对应生成记录的源图片
DELETE si FROM source_images si
LEFT JOIN generations g ON si.generation_id = g.id
WHERE g.id IS NULL;
```

### 批量清理失败数据
```sql
-- 一次性清理所有失败相关数据
START TRANSACTION;

-- 1. 删除失败的生成记录（会自动删除关联图片）
DELETE FROM generations WHERE status = 'FAILED';

-- 2. 删除孤儿图片
DELETE gi FROM generation_images gi
LEFT JOIN generations g ON gi.generation_id = g.id
WHERE g.id IS NULL;

-- 3. 删除孤儿源图片
DELETE si FROM source_images si
LEFT JOIN generations g ON si.generation_id = g.id
WHERE g.id IS NULL;

COMMIT;
```

---

## 📊 数据查询统计

### 生成任务统计
```sql
-- 各状态的生成任务数量
SELECT status, COUNT(*) as count
FROM generations
GROUP BY status;

-- 今日生成统计
SELECT 
    status,
    COUNT(*) as count,
    SUM(cost) as total_cost
FROM generations
WHERE DATE(created_at) = CURDATE()
GROUP BY status;

-- 用户生成排行榜（本月）
SELECT 
    u.nickname,
    u.uuid,
    COUNT(g.id) as generation_count,
    SUM(g.cost) as total_cost
FROM users u
JOIN generations g ON u.id = g.user_id
WHERE g.created_at >= DATE_FORMAT(NOW(), '%Y-%m-01')
GROUP BY u.id
ORDER BY generation_count DESC
LIMIT 10;
```

### 风格使用统计
```sql
-- 最受欢迎的风格
SELECT 
    s.name,
    s.model_identifier,
    COUNT(g.id) as usage_count,
    COUNT(CASE WHEN g.status = 'SUCCESS' THEN 1 END) as success_count,
    ROUND(COUNT(CASE WHEN g.status = 'SUCCESS' THEN 1 END) * 100.0 / COUNT(g.id), 2) as success_rate
FROM styles s
LEFT JOIN generations g ON s.id = g.style_id
GROUP BY s.id
ORDER BY usage_count DESC;
```

---

## 👥 用户管理

### 用户查询
```sql
-- 查找用户（支持多种方式）
SELECT u.*, ua.platform_uid
FROM users u
LEFT JOIN user_authentications ua ON u.id = ua.user_id
WHERE u.nickname LIKE '%张三%'
   OR u.phone = '13800138000'
   OR u.uuid = 'abc12345'
   OR ua.platform_uid = 'wx_openid_123';

-- 活跃用户统计（最近30天有生成记录）
SELECT 
    u.uuid,
    u.nickname,
    u.coins,
    COUNT(g.id) as recent_generations,
    MAX(g.created_at) as last_generation
FROM users u
JOIN generations g ON u.id = g.user_id
WHERE g.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY u.id
ORDER BY recent_generations DESC;
```

### 用户金币管理
```sql
-- 给指定ID的用户添加哇图币（带备注）
-- 方法1：通过用户ID添加
START TRANSACTION;

-- 更新用户金币余额
UPDATE users
SET coins = coins + 100
WHERE id = 123;

-- 记录金币流水（需要先获取用户当前余额）
INSERT INTO coin_transactions (user_id, type, amount, balance_after, description, created_at)
SELECT
    123,
    'ADMIN_GRANT',
    100,
    coins,
    '管理员手动充值 - 活动奖励'
FROM users
WHERE id = 123;

COMMIT;

-- 方法2：通过用户UUID添加（更安全）
START TRANSACTION;

-- 更新用户金币余额
UPDATE users
SET coins = coins + 100
WHERE uuid = 'abc12345';

-- 记录金币流水
INSERT INTO coin_transactions (user_id, type, amount, balance_after, description, created_at)
SELECT
    u.id,
    'ADMIN_GRANT',
    100,
    u.coins,
    '管理员手动充值 - 补偿用户生成失败'
FROM users u
WHERE u.uuid = 'abc12345';

COMMIT;

-- 方法3：通过手机号添加
START TRANSACTION;

UPDATE users
SET coins = coins + 50
WHERE phone = '13800138000';

INSERT INTO coin_transactions (user_id, type, amount, balance_after, description, created_at)
SELECT
    u.id,
    'ADMIN_GRANT',
    50,
    u.coins,
    '管理员手动充值 - 新用户注册奖励'
FROM users u
WHERE u.phone = '13800138000';

COMMIT;

-- 批量给多个用户添加金币
START TRANSACTION;

-- 给指定的多个用户ID添加金币
UPDATE users
SET coins = coins + 20
WHERE id IN (123, 456, 789);

-- 批量记录流水
INSERT INTO coin_transactions (user_id, type, amount, balance_after, description, created_at)
SELECT
    u.id,
    'ADMIN_GRANT',
    20,
    u.coins,
    '管理员批量充值 - 节日活动奖励'
FROM users u
WHERE u.id IN (123, 456, 789);

COMMIT;

-- 查看用户金币流水
SELECT
    ct.type,
    ct.amount,
    ct.balance_after,
    ct.description,
    ct.created_at
FROM coin_transactions ct
JOIN users u ON ct.user_id = u.id
WHERE u.uuid = 'abc12345'
ORDER BY ct.created_at DESC
LIMIT 20;

-- 查看管理员充值记录
SELECT
    u.uuid,
    u.nickname,
    ct.amount,
    ct.balance_after,
    ct.description,
    ct.created_at
FROM coin_transactions ct
JOIN users u ON ct.user_id = u.id
WHERE ct.type = 'ADMIN_GRANT'
AND ct.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
ORDER BY ct.created_at DESC;
```

---

## 💰 金币管理专区

### 常用金币操作模板
```sql
-- 🎁 新用户注册奖励（10金币）
START TRANSACTION;
UPDATE users SET coins = coins + 10 WHERE id = ?;
INSERT INTO coin_transactions (user_id, type, amount, balance_after, description, created_at)
SELECT id, 'ADMIN_GRANT', 10, coins, '新用户注册奖励' FROM users WHERE id = ?;
COMMIT;

-- 🎉 活动奖励（50金币）
START TRANSACTION;
UPDATE users SET coins = coins + 50 WHERE uuid = ?;
INSERT INTO coin_transactions (user_id, type, amount, balance_after, description, created_at)
SELECT id, 'ADMIN_GRANT', 50, coins, '春节活动奖励' FROM users WHERE uuid = ?;
COMMIT;

-- 🔧 生成失败补偿（按实际消耗金币补偿）
START TRANSACTION;
UPDATE users SET coins = coins + ? WHERE id = ?;
INSERT INTO coin_transactions (user_id, type, amount, balance_after, description, created_at)
SELECT id, 'ADMIN_GRANT', ?, coins, CONCAT('生成失败补偿 - 任务ID:', ?) FROM users WHERE id = ?;
COMMIT;

-- 🎯 VIP用户特殊奖励（100金币）
START TRANSACTION;
UPDATE users SET coins = coins + 100 WHERE phone = ?;
INSERT INTO coin_transactions (user_id, type, amount, balance_after, description, created_at)
SELECT id, 'ADMIN_GRANT', 100, coins, 'VIP用户专属奖励' FROM users WHERE phone = ?;
COMMIT;
```

### 金币操作验证
```sql
-- 操作前：查看用户当前金币
SELECT id, uuid, nickname, coins
FROM users
WHERE uuid = 'abc12345';

-- 操作后：验证金币变化
SELECT
    u.uuid,
    u.nickname,
    u.coins as current_coins,
    ct.amount as added_amount,
    ct.description,
    ct.created_at
FROM users u
JOIN coin_transactions ct ON u.id = ct.user_id
WHERE u.uuid = 'abc12345'
AND ct.type = 'ADMIN_GRANT'
ORDER BY ct.created_at DESC
LIMIT 1;
```

### 金币统计报表
```sql
-- 今日管理员充值统计
SELECT
    COUNT(*) as grant_count,
    SUM(amount) as total_granted,
    AVG(amount) as avg_amount
FROM coin_transactions
WHERE type = 'ADMIN_GRANT'
AND DATE(created_at) = CURDATE();

-- 按原因分类的充值统计（最近7天）
SELECT
    description,
    COUNT(*) as count,
    SUM(amount) as total_amount
FROM coin_transactions
WHERE type = 'ADMIN_GRANT'
AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY description
ORDER BY total_amount DESC;
```

---

## 🎨 图片生成管理

### 生成记录查询
```sql
-- 查看用户的生成历史
SELECT 
    g.id,
    g.task_id,
    s.name as style_name,
    g.status,
    g.cost,
    g.created_at,
    g.completed_at,
    COUNT(gi.id) as image_count
FROM generations g
JOIN users u ON g.user_id = u.id
JOIN styles s ON g.style_id = s.id
LEFT JOIN generation_images gi ON g.id = gi.generation_id
WHERE u.uuid = 'abc12345'
GROUP BY g.id
ORDER BY g.created_at DESC
LIMIT 20;

-- 查看生成的图片
SELECT 
    g.id as generation_id,
    g.task_id,
    gi.image_url,
    gi.created_at
FROM generations g
JOIN generation_images gi ON g.id = gi.generation_id
WHERE g.id = 12345;
```

### 问题排查
```sql
-- 查找长时间处理中的任务
SELECT 
    g.id,
    g.task_id,
    u.nickname,
    s.name as style_name,
    g.status,
    g.created_at,
    TIMESTAMPDIFF(MINUTE, g.created_at, NOW()) as processing_minutes
FROM generations g
JOIN users u ON g.user_id = u.id
JOIN styles s ON g.style_id = s.id
WHERE g.status IN ('PENDING', 'PROCESSING')
AND g.created_at < DATE_SUB(NOW(), INTERVAL 10 MINUTE)
ORDER BY g.created_at;

-- 查找错误频繁的任务
SELECT 
    error_message,
    COUNT(*) as error_count,
    MAX(created_at) as last_error
FROM generations
WHERE status = 'FAILED'
AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
GROUP BY error_message
ORDER BY error_count DESC;
```

---

## 🔧 数据库维护

### 表结构查看
```sql
-- 查看表结构
DESCRIBE generations;
DESCRIBE users;
DESCRIBE styles;

-- 查看表大小
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)',
    table_rows
FROM information_schema.tables
WHERE table_schema = 'wowpic'
ORDER BY (data_length + index_length) DESC;
```

### 索引优化
```sql
-- 查看表的索引
SHOW INDEX FROM generations;

-- 添加常用查询的索引
CREATE INDEX idx_generations_user_status ON generations(user_id, status);
CREATE INDEX idx_generations_created_at ON generations(created_at);
CREATE INDEX idx_generation_images_generation_id ON generation_images(generation_id);
```

### 数据清理
```sql
-- 清理30天前的成功记录（保留失败记录用于分析）
DELETE g, gi, si 
FROM generations g
LEFT JOIN generation_images gi ON g.id = gi.generation_id
LEFT JOIN source_images si ON g.id = si.generation_id
WHERE g.status = 'SUCCESS'
AND g.created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 清理无效的认证记录
DELETE ua FROM user_authentications ua
LEFT JOIN users u ON ua.user_id = u.id
WHERE u.id IS NULL;
```

---

## ⚡ 性能优化

### 慢查询分析
```sql
-- 开启慢查询日志
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;

-- 查看当前连接
SHOW PROCESSLIST;

-- 查看表锁情况
SHOW OPEN TABLES WHERE In_use > 0;
```

### 常用优化查询
```sql
-- 使用索引的用户生成历史查询
SELECT 
    g.id,
    g.status,
    g.created_at,
    s.name
FROM generations g
FORCE INDEX (idx_generations_user_status)
JOIN styles s ON g.style_id = s.id
WHERE g.user_id = 123
AND g.status = 'SUCCESS'
ORDER BY g.created_at DESC
LIMIT 10;

-- 分页查询优化（使用子查询）
SELECT g.*, s.name as style_name
FROM generations g
JOIN styles s ON g.style_id = s.id
WHERE g.id IN (
    SELECT id FROM generations
    WHERE user_id = 123
    ORDER BY created_at DESC
    LIMIT 20 OFFSET 100
)
ORDER BY g.created_at DESC;
```

---

## 🚨 紧急操作

### 紧急停止所有生成任务
```sql
-- 将所有处理中的任务标记为失败
UPDATE generations 
SET status = 'FAILED', 
    error_message = '系统维护，任务已取消',
    completed_at = NOW()
WHERE status IN ('PENDING', 'PROCESSING');
```

### 数据备份
```sql
-- 备份重要表
CREATE TABLE generations_backup AS SELECT * FROM generations;
CREATE TABLE users_backup AS SELECT * FROM users;

-- 恢复数据
INSERT INTO generations SELECT * FROM generations_backup;
```

---

## 📝 注意事项

1. **删除操作前务必备份**：所有DELETE操作都是不可逆的
2. **使用事务**：批量操作建议使用事务，出错可以回滚
3. **索引维护**：定期检查和优化索引
4. **监控性能**：关注慢查询和表大小
5. **数据一致性**：删除时注意外键约束和级联删除
