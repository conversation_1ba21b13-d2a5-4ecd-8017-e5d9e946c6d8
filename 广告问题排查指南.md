# 激励视频广告问题排查指南

## 当前问题分析

你遇到的错误：
```
TypeError: Cannot read property 'navigateBackInterceptionHandler' of undefined
激励视频广告重试失败 {errMsg: "video-ad has been destroyed"}
```

这是微信小程序广告组件的已知问题，主要原因：

### 1. 微信开发者工具的限制
- 开发者工具对广告组件的支持不完善
- 某些版本的基础库存在广告组件bug
- 开发环境和真机环境表现不一致

### 2. 广告实例生命周期问题
- 广告实例可能被微信小程序系统意外回收
- 页面切换时广告实例状态异常
- 重复创建/销毁广告实例导致的内存泄漏

## 已实施的解决方案

### ✅ 代码优化
1. **延迟初始化**：避免微信小程序的时序问题
2. **简化错误处理**：减少重复初始化，避免无限循环
3. **先加载再显示**：确保广告准备就绪后再显示
4. **超时保护**：防止长时间等待

### ✅ 错误分类处理
- `destroyed`：广告实例被销毁
- `not ready`：广告未准备好
- 其他错误：网络或配置问题

## 测试步骤

### 1. 开发者工具测试
```bash
# 1. 检查基础库版本
# 建议使用 2.8.0 或更高版本

# 2. 检查广告位ID
# 确认 adUnitId: 'adunit-b8fe48de4a45e35e' 是否正确

# 3. 查看控制台日志
# 观察广告初始化和显示过程的日志
```

### 2. 真机测试
```bash
# 1. 上传体验版
# 2. 在真机上测试广告功能
# 3. 检查是否还有相同错误
```

### 3. 网络环境检查
```bash
# 确保网络连接正常
# 检查是否能正常访问微信广告服务器
```

## 临时解决方案

如果问题仍然存在，可以考虑以下方案：

### 方案1：降级处理
```javascript
// 在广告功能不可用时，提供其他获取哇图币的方式
if (!this.videoAd) {
    // 显示其他获取哇图币的选项
    // 比如：签到、分享、邀请好友等
}
```

### 方案2：延迟重试
```javascript
// 增加重试间隔，避免频繁操作
setTimeout(() => {
    this.initRewardedVideoAd()
}, 5000) // 5秒后重试
```

### 方案3：用户手动重试
```javascript
// 让用户主动重新初始化广告
showRetryButton() {
    uni.showModal({
        title: '广告功能异常',
        content: '是否重新初始化广告功能？',
        success: (res) => {
            if (res.confirm) {
                this.initRewardedVideoAd()
            }
        }
    })
}
```

## 生产环境建议

### 1. 申请正式广告位
- 当前使用的是测试广告位ID
- 需要在微信小程序后台申请正式的激励视频广告位
- 替换为真实的广告位ID

### 2. 配置云端环境
```javascript
// config.js 中切换到生产环境
export const baseUrl = 'https://wowpic.hxlsf.com';
export const imageUrl = 'https://wowpic.hxlsf.com/static';
```

### 3. 监控和日志
- 添加广告功能的使用统计
- 记录广告失败的详细日志
- 监控用户反馈

## 常见问题FAQ

### Q: 为什么开发者工具中广告经常失败？
A: 这是正常现象，开发者工具对广告组件支持有限，建议在真机上测试。

### Q: 如何判断广告功能是否正常？
A: 查看控制台是否有"激励视频广告加载成功"的日志。

### Q: 广告显示失败后如何处理？
A: 当前代码会自动重试，如果仍然失败会提示用户稍后重试。

### Q: 如何提高广告成功率？
A: 
1. 确保网络连接稳定
2. 使用最新版本的微信小程序基础库
3. 在真机环境测试
4. 申请正式的广告位ID

## 下一步行动

1. **立即测试**：在真机上测试当前修改后的代码
2. **申请广告位**：如果功能正常，申请正式广告位
3. **部署生产**：切换到生产环境配置
4. **监控反馈**：收集用户使用反馈，持续优化
