from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, Enum, JSON, ForeignKey, Date, BIGINT
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import enum
import uuid

Base = declarative_base()

class TemplateType(enum.Enum):
    FULL_PROMPT = "FULL_PROMPT"
    VARIABLE_PROMPT = "VARIABLE_PROMPT"

class GenerationStatus(enum.Enum):
    PENDING = "PENDING"
    PROCESSING = "PROCESSING"
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"
    PARTIAL = "PARTIAL"  # 新增部分成功状态，表示部分图片生成成功

class User(Base):
    """用户表 - 存储用户核心档案信息"""
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    # 将 UUID 缩短为 8 位十六进制字符串，既简洁又拥有约 42 亿种组合
    uuid = Column(String(8), unique=True, nullable=False,
                 default=lambda: uuid.uuid4().hex[:8],
                 comment='对外显示的用户ID (8位随机码)')
    nickname = Column(String(255), nullable=False, comment='用户昵称')
    avatar_url = Column(String(512), nullable=True, comment='用户头像URL')
    phone = Column(String(20), unique=True, nullable=True, comment='手机号，核心关联字段')
    coins = Column(Integer, nullable=False, default=0, comment='哇图币余额')
    last_checkin_date = Column(Date, nullable=True, comment='上次签到日期')
    last_login_at = Column(DateTime, nullable=True, comment='上次登录时间')
    is_banned = Column(Boolean, nullable=False, default=False, comment='是否被封禁')
    notes = Column(Text, nullable=True, comment='管理员备注')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    
    # 关系
    authentications = relationship("UserAuthentication", back_populates="user")
    generations = relationship("Generation", back_populates="user")

class UserAuthentication(Base):
    """用户认证表 - 存储用户在不同平台的身份凭证"""
    __tablename__ = 'user_authentications'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False, comment='关联到主用户')
    platform = Column(String(20), nullable=False, comment='平台标识，如WX、QQ、WEB')
    platform_uid = Column(String(128), nullable=False, comment='各平台的openid')
    platform_unionid = Column(String(128), nullable=True, comment='该平台的unionid')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    
    # 关系
    user = relationship("User", back_populates="authentications")
    
    # 唯一约束
    __table_args__ = (
        {'mysql_charset': 'utf8mb4'},
    )

class Style(Base):
    """风格表 - 存储可用的AI图片风格"""
    __tablename__ = 'styles'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(100), unique=True, nullable=False, comment='风格名称')
    description = Column(String(255), nullable=True, comment='风格简短描述')
    cover_image_url = Column(String(512), nullable=True, comment='风格封面图URL')
    template_type = Column(Enum(TemplateType), nullable=False, comment='提示词类型')
    prompt_template = Column(Text, nullable=True, comment='变量提示词的模板')
    template_variables = Column(JSON, nullable=True, comment='定义变量的输入控件')
    model_identifier = Column(String(100), nullable=False, comment='内部AI模型标识符')
    cost = Column(Integer, nullable=False, default=5, comment='每次消耗金币')
    is_popular = Column(Integer, nullable=False, default=0, comment='热门权重（0=非热门，值越大越靠前）')
    example_images = Column(JSON, nullable=True, comment='风格示例图片URLs数组')
    settings = Column(JSON, nullable=True, comment='灵活的风格级配置，例如图片数量限制')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    
    # 关系
    generations = relationship("Generation", back_populates="style")

class Generation(Base):
    """图片生成记录表 - 存储用户的每一次图片生成任务和结果"""
    __tablename__ = 'generations'
    
    id = Column(BIGINT, primary_key=True, autoincrement=True, comment='自增主键')
    task_id = Column(String(255), unique=True, nullable=False, comment='Celery Task ID')
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False, comment='用户ID')
    style_id = Column(Integer, ForeignKey('styles.id'), nullable=False, comment='风格ID')
    prompt = Column(Text, nullable=True, comment='最终生成时使用的完整提示词')
    images_count = Column(Integer, nullable=False, default=1, comment='预期生成图片数量')
    status = Column(Enum(GenerationStatus), nullable=False, default=GenerationStatus.PENDING, comment='任务状态')
    is_visible = Column(Boolean, nullable=False, default=True, comment='是否对用户可见')
    cost = Column(Integer, nullable=False, comment='本次消耗的金币')
    error_message = Column(Text, nullable=True, comment='失败原因')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    completed_at = Column(DateTime, nullable=True, comment='任务完成时间')
    
    # 关系
    user = relationship("User", back_populates="generations")
    style = relationship("Style", back_populates="generations")
    images = relationship("GenerationImage", back_populates="generation", cascade="all, delete-orphan")
    # 新增：多源图片关系
    source_images = relationship("SourceImage", back_populates="generation", cascade="all, delete-orphan")

class GenerationImage(Base):
    """生成图片表 - 存储每次生成任务产出的多张图片"""
    __tablename__ = 'generation_images'
    
    id = Column(BIGINT, primary_key=True, autoincrement=True, comment='自增主键')
    generation_id = Column(BIGINT, ForeignKey('generations.id', ondelete='CASCADE'), nullable=False, comment='关联的生成任务ID')
    image_url = Column(String(512), nullable=False, comment='生成的图片URL')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    
    # 关系
    generation = relationship("Generation", back_populates="images")
    
    # 索引
    __table_args__ = (
        {'mysql_charset': 'utf8mb4'},
    )

class SourceImage(Base):
    """源图片表 - 存储用户上传的多张原始图片"""
    __tablename__ = 'source_images'

    id = Column(BIGINT, primary_key=True, autoincrement=True, comment='自增主键')
    generation_id = Column(BIGINT, ForeignKey('generations.id', ondelete='CASCADE'), nullable=False, comment='关联的生成任务ID')
    image_url = Column(String(512), nullable=False, comment='用户上传的原图URL')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')

    # 关系
    generation = relationship("Generation", back_populates="source_images")

    __table_args__ = (
        {'mysql_charset': 'utf8mb4'},
    )

# ================================
# 新增：充值订单与哇图币流水表定义
# ================================

class RechargeOrderStatus(enum.Enum):
    PENDING = "PENDING"
    PAID = "PAID"
    REFUND = "REFUND"
    CLOSED = "CLOSED"

class CoinTransactionSource(enum.Enum):
    RECHARGE = "RECHARGE"
    CHECKIN = "CHECKIN"
    VIDEO_AD = "VIDEO_AD"
    INVITE = "INVITE"
    GENERATE = "GENERATE"
    REFUND = "REFUND"
    MANUAL = "MANUAL"

class RechargeOrder(Base):
    """人民币 → 哇图币充值订单表"""
    __tablename__ = 'recharge_orders'

    id = Column(BIGINT, primary_key=True, autoincrement=True, comment='订单主键')
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False, comment='关联用户')
    out_trade_no = Column(String(32), unique=True, nullable=False, comment='商户订单号')
    prepay_id = Column(String(64), nullable=True, comment='预支付会话ID')
    coins = Column(Integer, nullable=False, comment='充值获得的哇图币数')
    amount_cny = Column(Integer, nullable=False, comment='充值金额(分)')
    status = Column(Enum(RechargeOrderStatus), nullable=False, default=RechargeOrderStatus.PENDING, index=True, comment='订单状态')
    pay_time = Column(DateTime, nullable=True, comment='支付完成时间')
    refund_time = Column(DateTime, nullable=True, comment='退款完成时间')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')

    # 关系
    user = relationship("User", backref="recharge_orders")

    __table_args__ = (
        {'mysql_charset': 'utf8mb4'},
    )

class CoinTransaction(Base):
    """哇图币变动流水表"""
    __tablename__ = 'coin_transactions'

    id = Column(BIGINT, primary_key=True, autoincrement=True, comment='流水主键')
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False, comment='关联用户')
    change = Column(Integer, nullable=False, comment='变动值；正数=收入，负数=支出')
    balance = Column(Integer, nullable=False, comment='变动后的余额')
    source = Column(Enum(CoinTransactionSource), nullable=False, comment='变动来源')
    source_id = Column(BIGINT, nullable=True, comment='关联业务表主键')
    remark = Column(String(255), nullable=True, comment='说明/备注')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')

    # 关系
    user = relationship("User", backref="coin_transactions")

    __table_args__ = (
        {'mysql_charset': 'utf8mb4'},
    )