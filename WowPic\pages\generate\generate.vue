<template>
	<view>
		<!-- 固定在顶部的风格提示 -->
		<view class="style-header fixed-header">
			<view class="back-btn" @click="navigateBack">
				<view class="back-icon"></view>
			</view>
			<view class="style-info" @click="showStyleSelector = true">
				<image class="style-icon" :src="styleInfo && styleInfo.cover_image_url ? getImageUrl(styleInfo.cover_image_url) : '/static/头像.png'"></image>
				<text class="style-text">{{currentStyle}}</text>
				<view class="down-icon"></view>
			</view>
		</view>
		
		<!-- 使用风格选择器组件 -->
		<StyleSelector 
			:show="showStyleSelector" 
			:currentStyleId="styleId"
			@select="switchStyle" 
			@cancel="showStyleSelector = false"
		/>
		

		
		<scroll-view class="generate-container no-scrollbar" scroll-y="true" show-scrollbar="false" enhanced="true" bounces="true">
			<!-- 顶部安全区域 + 头部占位 -->
			<view class="safe-area"></view>
			<view class="header-placeholder"></view>
		
		<!-- 上传图片区域 -->
<view class="upload-section">
	<view class="section-title-bar">
		<text class="section-title">上传图片</text>
		<text class="section-subtitle">正脸清晰照效果最佳</text>
	</view>
	
	<view class="multi-upload-container">
		<!-- 已上传的图片列表 - 仅当有图片时显示 -->
		<scroll-view v-if="tempImages.length > 0" class="uploaded-images-scroll" scroll-x="true" show-scrollbar="false" enhanced="true">
			<view class="uploaded-images-list">
				<view class="image-item" v-for="(img, index) in tempImages" :key="index">
					<image class="preview-image" :src="img" mode="aspectFill"></image>
					<view class="image-overlay">
						<view class="delete-icon" @click.stop="deleteImage(index)"></view>
					</view>
					<view class="image-index">{{index + 1}}</view>
				</view>
				
				<!-- 添加更多图片的按钮 -->
				<view class="image-item add-more" @click="chooseImage" v-if="tempImages.length < maxImagesCount">
					<view class="upload-icon"></view>
					<text class="add-text">添加图片</text>
				</view>
			</view>
		</scroll-view>
		
		<!-- 空状态 - 当没有图片时显示 -->
		<view v-if="tempImages.length === 0" class="upload-box empty" @click="chooseImage">
			<view class="upload-icon large"></view>
			<view class="upload-overlay">
				<text class="upload-text">点击上传图片</text>
			</view>
		</view>
	</view>
</view>
		
		<!-- 1. 提示控件 (FULL_PROMPT类型) -->
		<view class="prompt-section" v-if="styleInfo && styleInfo.template_type === 'FULL_PROMPT'">
			<view class="section-title-bar">
				<text class="section-title">提示词</text>
				<text class="section-subtitle">详细的提示能带来更好的效果</text>
			</view>

			<view class="prompt-tip-box">
				<view class="tip-icon"></view>
				<view class="tip-content">
					<text class="tip-title">此风格无需输入提示词</text>
					<text class="tip-desc">只需上传您的图片，AI将根据"{{currentStyle}}"风格自动生成效果</text>
				</view>
			</view>
		</view>

		<!-- 2. 输入控件 (VARIABLE_PROMPT类型且存在detail变量) -->
		<view class="prompt-section" v-if="styleInfo && styleInfo.template_type === 'VARIABLE_PROMPT' && styleInfo.template_variables && styleInfo.template_variables.some(v => v.name === 'detail')">
			<view class="section-title-bar">
				<text class="section-title">提示词</text>
				<text class="section-subtitle">详细的提示能带来更好的效果</text>
			</view>

			<view class="prompt-input-box">
				<textarea class="prompt-input" placeholder="描述你想要的效果，例如：动漫风格、彩色头发、笑容..." v-model="prompt" maxlength="1000" />
				<view class="prompt-length">{{prompt.length}}/3000</view>
			</view>

			<view class="prompt-tags">
				<view class="tag" @click="addTag('请确保角色面部五官等细节完全一致')">请确保角色面部五官等细节完全一致</view>
				<!-- <view class="tag" @click="addTag('彩色头发')">彩色头发</view>
				<view class="tag" @click="addTag('笑容')">笑容</view>
				<view class="tag" @click="addTag('夏日')">夏日</view> -->
			</view>
		</view>

		<!-- 3. 选项控件 (VARIABLE_PROMPT类型且存在select变量) -->
		<template v-if="styleInfo && styleInfo.template_type === 'VARIABLE_PROMPT' && styleInfo.template_variables">
			<view v-for="(variable, index) in styleInfo.template_variables.filter((v) => v.type === 'select')" :key="index" class="variable-selector-box">
				<text class="variable-label">{{variable.label || variable.name}}</text>
				<view v-if="variable.type === 'select' && variable.options" class="select-options">
					<!-- 标准选项 -->
					<view
						v-for="(option, optIndex) in variable.options.filter((opt) => !opt.custom_field)"
						:key="optIndex"
						class="option-btn"
						:class="{'active': variableValues[variable.name] === option.value}"
						@click="selectVariableOption(variable.name, option.value, false)"
					>
						<text class="option-text">{{option.label}}</text>
					</view>

					<!-- 自定义选项（如果有） -->
					<view v-if="hasCustomOption(variable)" class="option-custom" :class="{'option-custom-active': isCustomSelected(variable.name)}">
						<view
							class="option-btn custom-option"
							:class="{'active': isCustomSelected(variable.name), 'narrow': isCustomSelected(variable.name)}"
							@click="selectCustomOption(variable.name)"
						>
							<text class="option-text">自定义</text>
						</view>

						<!-- 内联输入框 -->
						<input
							v-if="isCustomSelected(variable.name)"
							class="inline-input"
							:placeholder="getCustomPlaceholder(variable)"
							v-model="customValues[variable.name]"
							:focus="isCustomSelected(variable.name)"
						/>
					</view>
				</view>
			</view>
		</template>

		<!-- 比例选择 (对所有风格都显示) -->
		<view class="aspect-ratio-options">
			<view
				class="aspect-btn"
				:class="{'active': selectedRatioIndex === 0}"
				@click="selectRatio(0)"
			>
				<view class="aspect-icon square"></view>
				<text class="aspect-btn-text">正方形</text>
			</view>
			<view
				class="aspect-btn"
				:class="{'active': selectedRatioIndex === 1}"
				@click="selectRatio(1)"
			>
				<view class="aspect-icon portrait"></view>
				<text class="aspect-btn-text">竖屏</text>
			</view>
			<view
				class="aspect-btn"
				:class="{'active': selectedRatioIndex === 2}"
				@click="selectRatio(2)"
			>
				<view class="aspect-icon landscape"></view>
				<text class="aspect-btn-text">横屏</text>
			</view>
		</view>
		
		<!-- 底部操作区 -->
		<view class="bottom-action">
			<view class="coin-info">
				<image class="coin-icon" src="/static/coins.png"></image>
				<view class="coin-text">{{styleInfo ? styleInfo.cost : 99}} 哇图币/次</view>
			</view>
			<view class="generate-btn" @click="generateImage" :class="{'disabled': !tempImages.length || generationInProgress}">
				<text>{{generationInProgress ? '生成中...' : '开始生成'}}</text>
				<view class="btn-icon" v-if="!generationInProgress"></view>
			</view>
		</view>
		
		<!-- 结果区域 -->
		<view class="result-section">
			<view class="section-title-bar">
				<text class="section-title">生成结果</text>
				<text class="section-subtitle">{{ (generatedImage || (generatedImages && generatedImages.length > 0)) ? 'AI生成不可控，不承诺任何最终效果' : (generationInProgress ? '您可以离开此页面，稍后在我的作品中查看' : (generationError ? '生成失败，请重试或联系客服' : '点击上方按钮开始生成')) }}</text>
			</view>
			
			<!-- 生成失败状态 -->
			<view v-if="generationError" class="error-status">
				<view class="error-icon"></view>
				<view class="error-content">
					<text class="error-text">{{generationError}}</text>
				</view>
			</view>
			


			<!-- 生成中状态 -->
			<view v-if="showGeneratingStatus" class="generating-status"></view>

			<!-- 未生成状态 -->
			<view v-if="showWaitingStatus" class="waiting-status">
				<view class="waiting-icon"></view>
				<view class="waiting-text">请点击"开始生成"，AI将为您带来惊喜...</view>
			</view>

			<!-- 生成结果 -->
			<view v-if="showResultBox" class="result-box">
				<!-- 替换轮播为垂直平铺布局 -->
				<view v-if="generatedImages && generatedImages.length > 0" class="result-images-list">
					<view v-for="(imageUrl, idx) in generatedImages" :key="idx" class="result-image-item">
						<!-- 添加图片加载状态控制 -->
						<view class="image-loading-container" v-if="!imageLoadedStatus[idx]">
							<view class="image-loading-animation"></view>
						</view>
						<image 
							class="result-image" 
							:class="{'hidden': !imageLoadedStatus[idx]}"
							:src="getImageUrl(imageUrl)" 
							mode="widthFix" 
							@tap="previewImage(imageUrl)"
							@load="onImageLoaded(idx)"
							@error="onImageLoadError(idx)"
						></image>
					</view>
				</view>
				<!-- 无图情况处理 -->
				<view v-else class="image-container">
					<view class="image-loading-container" v-if="!singleImageLoaded">
						<view class="image-loading-animation"></view>
					</view>
					<image 
						class="result-image" 
						:class="{'hidden': !singleImageLoaded}"
						:src="getImageUrl(generatedImage)" 
						mode="widthFix" 
						@tap="previewImage(generatedImage)"
						@load="onSingleImageLoaded"
						@error="onSingleImageLoadError"
					></image>
				</view>
			</view>
			
			<!-- 操作按钮 - 与section-title-bar同级 -->
			<view v-if="generatedImage || (generatedImages && generatedImages.length > 0)" class="action-row">
				<view class="action-btn save-btn" @click="saveImage">
					<view class="action-icon save-icon"></view>
					<text>保存图片</text>
				</view>
				<button class="action-btn share-btn" open-type="share" @click="prepareShare">
					<view class="action-icon share-icon"></view>
					<text>分享作品</text>
				</button>
			</view>
		</view>
	</scroll-view>
</view>

	<!-- 公告弹窗 -->
	<NoticeModal
		:show="showNotice"
		:noticeData="noticeData"
		@close="closeNotice">
	</NoticeModal>
</template>

<script>
	import request from '../../utils/request.js'
	import { getImageUrl } from '../../utils/config.js'
	import SkeletonLoading from '../../components/SkeletonLoading.vue'
	import StyleSelector from '../../components/StyleSelector.vue'
	import NoticeModal from '../../components/NoticeModal.vue'
	import imageMixin from '../../utils/imageMixin.js'

	export default {
		components: {
			SkeletonLoading,
			StyleSelector,
			NoticeModal
		},
		mixins: [imageMixin],

		computed: {
			// 是否显示生成中状态
			showGeneratingStatus() {
				const hasImages = this.generatedImage || (this.generatedImages && this.generatedImages.length > 0);
				return this.generationInProgress && !hasImages && !this.generationError;
			},

			// 是否显示等待状态
			showWaitingStatus() {
				const hasImages = this.generatedImage || (this.generatedImages && this.generatedImages.length > 0);
				return !hasImages && !this.generationError && !this.generationInProgress;
			},

			// 是否显示结果区域
			showResultBox() {
				return this.generatedImage || (this.generatedImages && this.generatedImages.length > 0);
			}
		},

		data() {
			return {
				currentStyle: '未指定风格',
				prompt: '', // 用户文本输入（保留用于单变量快捷绑定）
				variables: {}, // VARIABLE_PROMPT 风格提交对象
				lastPrefill: '', // 新增：用于存储上一次的预填充内容，防止覆盖用户输入
				// 新增：背景样式选择相关数据
				selectedBackgroundStyle: '白底', // 默认选择白底
				customBackgroundStyle: '', // 自定义背景样式
				showCustomBackgroundInput: false, // 控制是否显示自定义输入框

				generatedImage: '', // 兼容旧版单图
				generatedImages: [], // 新增：存储多张生成图片
				styleId: null,
				styleInfo: null, // 存储风格详细信息
				isGenerating: false, // 是否正在生成图片
				generationInProgress: false,  // 表示是否有正在进行的生成任务
				generationTaskId: '',         // 当前生成任务ID
				generationStatus: '',         // 当前生成状态 (PENDING, SUCCESS, FAILED)
				generationCheckTimer: null,    // 状态检查定时器
				generationError: '',           // 生成失败时的错误信息
				currentImageIndex: 0,          // 当前显示的图片索引（用于多图操作）
				// 移除imagesCount参数，不再支持指定生成数量
				currentShareImageUrl: '',      // 当前要分享的图片URL
				userCoins: 0, // 用户当前哇图币余额
				isFromHistory: false, // 是否从历史记录加载
				currentGenerationId: null, // 当前生成记录ID
				// 新增比例选择相关数据 - 简化版
				selectedRatioIndex: 1, // 默认选择竖屏比例(3:4)
				aspectRatios: [
					// 更新比例值，仅保留纯比例字符串，后续逻辑统一拼接 "ratio" 字段
					{ label: '正方形', value: '1:1' },
					{ label: '竖屏', value: '3:4' },
					{ label: '横屏', value: '4:3' }
				],
				// 订阅消息模板ID（运行时从后端获取）
				subscriptionTemplateId: '',
				// 新增：图片加载状态跟踪
				imageLoadedStatus: [], // 多图加载状态
				singleImageLoaded: false, // 单图加载状态

				// 风格选择器相关
				showStyleSelector: false, // 控制风格选择器的显示与隐藏
				variableValues: {}, // 存储变量值
				showCustomFields: {}, // 控制自定义输入框的显示
				customValues: {}, // 存储自定义输入值

				// 公告相关
				showNotice: false, // 控制公告弹窗的显示
				noticeData: null // 公告数据
			}
		},
		// 自定义分享配置（仅小程序端生效）
		onShareAppMessage() {
			// 简化分享逻辑，直接分享第一张图片
			const img = this.getImageUrl(this.currentShareImageUrl || (this.generatedImages && this.generatedImages.length > 0 ? this.generatedImages[0] : this.generatedImage));
			return {
				title: this.getShareTitle(),
				path: '/pages/index/index', // 分享后点击进入首页
				imageUrl: img
			};
		},

		// 分享到朋友圈配置
		onShareTimeline() {
			const img = this.getImageUrl(this.currentShareImageUrl || (this.generatedImages && this.generatedImages.length > 0 ? this.generatedImages[0] : this.generatedImage));
			return {
				title: this.getShareTitle(),
				imageUrl: img
			};
		},
		onLoad(options) {
			console.log('generate页面onLoad, 接收的options:', options);
			
			// 先获取订阅消息模板ID
			this.fetchSubscribeTemplateId();
			// 如果传入了风格ID，则使用传入的风格
			if (options.styleId) {
				console.log('接收到风格ID:', options.styleId);
				this.styleId = parseInt(options.styleId);
				console.log('转换后的styleId:', this.styleId);
				this.loadStyleInfo();
			} else {
				console.log('未接收到风格ID');
			}
			
			// 如果传入了生成ID，则加载该生成的结果
			if (options.generationId) {
				this.loadGenerationResult(options.generationId);
			}
			
			// 加载用户信息
			this.loadUserInfo();
		},
		
		onShow() {
			// 每次显示页面时更新用户信息（可能从其他页面修改了哇图币）
			this.loadUserInfo();

			// 启用分享菜单
			uni.showShareMenu({
				withShareTicket: true,
				menus: ['shareAppMessage']
			});
		},

		onHide() {
			// 页面隐藏时暂停定时器，避免后台运行
			if (this.generationCheckTimer) {
				clearInterval(this.generationCheckTimer);
				this.generationCheckTimer = null;
			}
		},

		onUnload() {
			// 清除定时器
			if (this.generationCheckTimer) {
				clearInterval(this.generationCheckTimer);
				this.generationCheckTimer = null;
			}
			
			// 重置从历史记录加载的标记
			this.isFromHistory = false;
			this.currentGenerationId = null;
		},
		methods: {
			// 将导入的getImageUrl添加到方法中，使其在模板中可用
			getImageUrl,
			
			// 生成分享标题
			getShareTitle() {
				const titles = [
					'哇哇哇哇哇！！！',
					// 'AI 也能玩出新花样？看看我的作品！',
					// '点开看看，这可是 AI 的神奇创作哦~',
					// '快来围观！AI 帮我生成的大片级美图',
				];
				return titles[Math.floor(Math.random() * titles.length)];
			},
			// 获取风格详情
			async loadStyleInfo() {
				if (!this.styleId) {
					console.log('loadStyleInfo: 无有效的风格ID');
					return Promise.resolve();
				}
				
				console.log('开始加载风格信息, styleId =', this.styleId);
				
				return new Promise((resolve, reject) => {
					request.get(`/wowpic/styles/${this.styleId}`).then(res => {
						console.log('风格信息加载成功:', res);
						this.styleInfo = res;
						this.currentStyle = res.name;
						// 如果是全文提示词类型，自动填充prompt
						if (res.template_type === 'FULL_PROMPT') {
							this.prompt = res.prompt_template || '';
						}
						
						// 初始化变量值
						if (res.template_type === 'VARIABLE_PROMPT' && res.template_variables) {
							// 初始化变量值存储
							res.template_variables.forEach(variable => {
								// 如果是选择类型并且有选项，选择第一个选项作为默认值
								if (variable.type === 'select' && variable.options && variable.options.length > 0) {
									this.variableValues[variable.name] = variable.options[0].value;
									// 检查是否有custom_field标记
									if (variable.options[0].custom_field) {
										this.showCustomFields[variable.name] = true;
									}
								} else {
									// 其他类型变量默认为空
									this.variableValues[variable.name] = '';
								}
							});
						}
						
						// 加载成功后，首次获取预填充
						this.fetchPrefillPrompt(this.tempImages.length);

						// 根据风格配置调整可上传图片数量
						if (res.settings && res.settings.max_source_images) {
							this.maxImagesCount = res.settings.max_source_images;
						} else if (res.model_identifier === 'pet_id_photo_v1') {
							this.maxImagesCount = 1; // 后端未返回 settings 时的兜底
						} else {
							this.maxImagesCount = 4; // 默认
						}

						// 加载风格相关公告
						this.loadStyleNotice();

						resolve(res);
					}).catch(err => {
						console.error('获取风格详情失败', err);
						console.log('错误详情:', JSON.stringify(err));
						
						// 设置默认的风格信息，确保界面正常显示
						if (!this.styleInfo) {
							this.styleInfo = {
								name: this.currentStyle || '未知风格',
								cost: 99,
								template_type: 'VARIABLE_PROMPT'
							};
						}
						
						uni.showToast({
							title: '获取风格信息失败',
							icon: 'none',
							duration: 2000
						});
						
						reject(err);
					});
				});
			},
			// 直接使用导入的 getImageUrl 函数
			navigateBack() {
				// 获取当前页面栈
				const pages = getCurrentPages();
				// 如果页面栈长度大于1，说明有上一级页面，可以返回
				if (pages.length > 1) {
					uni.navigateBack();
				} else {
					// 如果是第一个页面，则跳转到首页
					uni.switchTab({
						url: '/pages/index/index'
					});
				}
			},
			// 图片变化回调（覆盖mixin中的空方法）
			onImagesChanged() {
				// 当图片数量变化时，获取预填充提示词
				this.fetchPrefillPrompt(this.tempImages.length);
			},
			// 新增比例选择方法
			selectRatio(index) {
				this.selectedRatioIndex = index;
			},
			
			addTag(tag) {
				if (this.prompt.length > 0 && !this.prompt.endsWith('、') && !this.prompt.endsWith('，') && !this.prompt.endsWith(' ')) {
					this.prompt += '。';
				}
				this.prompt += tag;
			},

			// 从完整提示词中解析变量值
			parseVariableValuesFromPrompt(fullPrompt) {
				if (!fullPrompt || !this.styleInfo || !this.styleInfo.template_variables) {
					return;
				}

				const template = this.styleInfo.prompt_template;
				const variables = this.styleInfo.template_variables;

				// 移除可能的ratio信息
				let cleanPrompt = fullPrompt;
				if (cleanPrompt.includes('\n"ratio"')) {
					cleanPrompt = cleanPrompt.substring(0, cleanPrompt.indexOf('\n"ratio"'));
				}

				// 特殊处理：如果只有一个detail变量
				const detailVariable = variables.find(v => v.name === 'detail');
				if (variables.length === 1 && detailVariable) {
					this.parseDetailVariable(template, cleanPrompt, detailVariable);
					return;
				}

				// 处理多变量情况：尝试反向解析
				this.parseMultipleVariables(template, cleanPrompt, variables);
			},

			// 解析单个detail变量
			parseDetailVariable(template, cleanPrompt, detailVariable) {
				// 情况1：自由创作风格 - prompt_template为空字符串
				if (!template || template.trim() === '') {
					const trimmedPrompt = cleanPrompt.trim();
					this.prompt = trimmedPrompt;
					this.variableValues[detailVariable.name] = trimmedPrompt;
					return;
				}
				// 情况2：模板就是纯{detail}
				if (template === '{detail}') {
					this.prompt = cleanPrompt.trim();
					this.variableValues[detailVariable.name] = this.prompt;
					return;
				}
				// 情况3：模板以{detail}结尾
				if (template.endsWith('{detail}')) {
					const templatePrefix = template.substring(0, template.length - '{detail}'.length);
					if (cleanPrompt.startsWith(templatePrefix)) {
						this.prompt = cleanPrompt.substring(templatePrefix.length).trim();
						this.variableValues[detailVariable.name] = this.prompt;
					}
				}
			},

			// 解析多个变量
			parseMultipleVariables(template, cleanPrompt, variables) {
				const parsedValues = this.reverseParseTemplate(template, cleanPrompt, variables);

				// 应用解析结果
				variables.forEach(variable => {
					if (parsedValues[variable.name] !== undefined) {
						if (variable.name === 'detail') {
							this.prompt = parsedValues[variable.name];
						}

						if (variable.type === 'select' && variable.options) {
							this.applySelectVariableValue(variable, parsedValues[variable.name]);
						} else {
							this.variableValues[variable.name] = parsedValues[variable.name];
						}
					}
				});
			},

			// 应用选择类型变量的值
			applySelectVariableValue(variable, value) {
				const matchedOption = variable.options.find(opt =>
					opt.value === value || (opt.custom_field && value !== opt.value)
				);

				if (matchedOption) {
					if (matchedOption.custom_field && value !== matchedOption.value) {
						// 自定义选项
						this.variableValues[variable.name] = 'custom';
						this.showCustomFields[variable.name] = true;
						this.customValues[variable.name] = value;
					} else {
						// 标准选项
						this.variableValues[variable.name] = matchedOption.value;
					}
				}
			},

			// 反向解析模板
			reverseParseTemplate(template, fullPrompt, variables) {
				const result = {};

				// 处理select类型变量
				variables.forEach(variable => {
					if (variable.type === 'select' && variable.options) {
						result[variable.name] = this.parseSelectVariable(variable, fullPrompt);
					}
				});

				// 处理detail类型变量
				const detailVar = variables.find(v => v.name === 'detail');
				if (detailVar && !result[detailVar.name]) {
					result[detailVar.name] = this.extractDetailFromPrompt(template, fullPrompt, variables, result);
				}

				return result;
			},

			// 解析选择类型变量
			parseSelectVariable(variable, fullPrompt) {
				// 按选项值长度降序排列，优先匹配长的选项值
				const sortedOptions = [...variable.options].sort((a, b) => b.value.length - a.value.length);

				for (const option of sortedOptions) {
					if (option.value !== 'custom' && fullPrompt.includes(option.value)) {
						return option.value;
					}
				}

				// 如果没有匹配到标准选项，且有自定义选项，返回空字符串让用户重新输入
				if (variable.options.some(opt => opt.custom_field)) {
					return '';
				}

				return undefined;
			},

			// 从提示词中提取detail值
			extractDetailFromPrompt(template, fullPrompt, variables, parsedValues) {
				// 创建一个临时模板，将已解析的变量替换掉
				let tempTemplate = template;
				variables.forEach(variable => {
					if (variable.name !== 'detail' && parsedValues[variable.name]) {
						tempTemplate = tempTemplate.replace(`{${variable.name}}`, parsedValues[variable.name]);
					}
				});

				// 如果模板末尾是{detail}
				if (tempTemplate.endsWith('{detail}')) {
					const prefix = tempTemplate.substring(0, tempTemplate.length - '{detail}'.length);
					if (fullPrompt.startsWith(prefix)) {
						return fullPrompt.substring(prefix.length).trim();
					}
				}

				return '';
			},
			
			// 加载历史生成记录 - 修改为支持多图
			async loadGenerationResult(generationId) {
				try {
					// 标记为从历史记录加载
					this.isFromHistory = true;
					this.currentGenerationId = generationId;
					
					uni.showLoading({ title: '加载中...' });
					const result = await request.get(`/wowpic/generate/${generationId}`);
					
					// 设置风格ID和名称
					this.styleId = result.style_id;
					this.currentStyle = result.style_name || '吉卜力风格';
					
					// 设置源图片，支持多图
					if (result.source_image_urls && result.source_image_urls.length > 0) {
						this.uploadedImageUrls = result.source_image_urls;
						this.tempImages = result.source_image_urls.map(url => this.getImageUrl(url));
					}
					
					// 加载完整风格信息，确保能正确判断是否显示提示词输入框
					if (this.styleId) {
						await this.loadStyleInfo();

						// 根据风格类型处理提示词和变量值
						if (this.styleInfo) {
							if (this.styleInfo.template_type === 'FULL_PROMPT') {
								// 全文提示词类型，直接使用模板
								this.prompt = this.styleInfo.prompt_template || '';
							} else if (this.styleInfo.template_type === 'VARIABLE_PROMPT') {
								// 变量提示词类型，需要解析变量值
								this.parseVariableValuesFromPrompt(result.prompt);
							}
						} else {
							// 如果没有风格信息，直接使用原始提示词
							this.prompt = result.prompt || '';
						}
					}
					
					// 处理不同状态
					if (result.status === 'SUCCESS' || result.status === 'PARTIAL') {
						// 处理多图情况
						this.handleGenerationSuccess(result);
						this.scrollToResult();
					} else if (result.status === 'FAILED') {
						this.generationError = result.error_message || '生成失败，请联系客服';
						this.scrollToResult();
					} else if (result.status === 'PENDING' || result.status === 'PROCESSING') {
						this.generationInProgress = true;
						this.generationTaskId = result.task_id;
						
						// 从服务器获取最新状态，以防生成已经完成但数据库尚未更新
						try {
							const latestStatus = await request.get(`/wowpic/generate/status/${result.task_id}`, {}, {
								hideErrorTips: true
							});
							
							// 如果最新状态与数据库中的状态不同，则使用最新状态
							if (latestStatus.status !== result.status) {
								console.log('检测到状态变化:', result.status, '->', latestStatus.status);
								
								// 如果已经成功，显示结果
								if (latestStatus.status === 'SUCCESS' || latestStatus.status === 'PARTIAL') {
									this.handleGenerationSuccess(latestStatus);
									this.generationInProgress = false;
									this.scrollToResult();
									
									// 显示提示
									uni.showToast({
										title: '生成已完成',
										icon: 'success',
										duration: 2000
									});
									
									uni.hideLoading();
									return;
								} 
								// 如果失败，显示错误
								else if (latestStatus.status === 'FAILED') {
									this.generationError = latestStatus.error_message || '生成失败，请联系客服';
									this.generationInProgress = false;
									this.scrollToResult();
									
									uni.hideLoading();
									return;
								}
							}
						} catch (statusErr) {
							console.error('获取最新状态失败:', statusErr);
							// 错误时继续使用原状态
						}
						
						// 开始定期检查状态
						this.startStatusCheck();
					}
					
					uni.hideLoading();
				} catch (e) {
					console.error('加载历史记录失败:', e);
					uni.hideLoading();
					
					// 设置错误信息以便在UI上显示
					this.generationError = '加载记录失败，请返回重试';
					
					// 显示友好的错误提示
					uni.showToast({
						title: '加载记录失败，请返回重试',
						icon: 'none',
						duration: 3000
					});
					
					// 如果有风格ID，尝试加载风格信息，保证界面显示正常
					if (this.styleId) {
						this.loadStyleInfo().catch(() => {
							// 如果风格信息也加载失败，设置默认值
							this.currentStyle = '未知风格';
						});
					}
				}
			},
			
			// 滚动到结果区域
			scrollToResult() {
				// 使用nextTick确保DOM已更新
				this.$nextTick(() => {
					uni.pageScrollTo({
						selector: '.result-section',
						duration: 300
					});
				});
			},
			
			// 检查生成状态
			async checkGenerationStatus() {
				if (!this.generationTaskId) return;
				
				try {
					const result = await request.get(`/wowpic/generate/status/${this.generationTaskId}`, {}, {
						hideErrorTips: true
					});
					
					this.generationStatus = result.status;
					
					if (result.status === 'SUCCESS' || result.status === 'PARTIAL') {
						// 先更新状态标志
						this.isGenerating = false;
						this.generationInProgress = false;
						clearInterval(this.generationCheckTimer);
						this.generationCheckTimer = null;
						uni.hideLoading();

						// 然后处理生成结果
						this.handleGenerationSuccess(result);
						
						// 显示成功提示
						uni.showToast({
							title: '生成完成',
							icon: 'success',
							duration: 2000
						});
						
						// 滚动到结果区域
						this.scrollToResult();
						
						// 生成成功后，发送订阅消息
						try {
							// 创建模板数据，符合模板格式要求
							const now = new Date();
							const currentTime = now.toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'});

							// 根据新的订阅消息模板字段组装数据
							const templateData = {
								thing1: {
									value: `${this.currentStyle || '默认'}风格已创作完成，点击查看>>`
								},
								phrase6: {
									value: '生成成功'
								},
								time5: {
									value: currentTime // 只传入时间，避免47003错误
								},
								thing2: {
									value: '打开小程序查看更多风格~'
								}
							};
							
							console.log('准备发送订阅消息通知，模板数据:', templateData);
							// 调用后端接口发送订阅消息（忽略失败，不阻塞主流程）
							try {
								await request.post('/wowpic/notify/send', {
									templateId: this.subscriptionTemplateId,
									data: templateData,
									page: '/pages/index/index',
									generationId: this.currentGenerationId
								}, { hideErrorTips: true });
							} catch (e) {
								console.error('调用订阅消息接口失败:', e);
							}
						} catch (notifyErr) {
							console.error('发送订阅消息通知失败:', notifyErr);
							// 通知失败不影响主流程
						}
						
						// 如果是从历史记录加载的，主动刷新页面数据
						if (this.isFromHistory) {
							// 更新完整的生成记录数据
							try {
								const generationId = this.currentGenerationId;
								if (generationId) {
									const fullData = await request.get(`/wowpic/generate/${generationId}`, {}, {
										hideErrorTips: true
									});

									// 更新生成图片URL - 使用Vue.set确保响应式更新
									if (fullData.generated_image_urls && fullData.generated_image_urls.length > 0) {
										// 检查是否是相同的图片URL，避免重复重置加载状态
										const isSameImages = this.generatedImages &&
											this.generatedImages.length === fullData.generated_image_urls.length &&
											this.generatedImages.every((url, index) => url === fullData.generated_image_urls[index]);

										this.$set(this, 'generatedImages', fullData.generated_image_urls);

										// 只有在图片URL发生变化时才重置加载状态
										if (!isSameImages) {
											this.$set(this, 'imageLoadedStatus', new Array(fullData.generated_image_urls.length).fill(false));
										}

										// 为了兼容，也设置第一张为generatedImage
										this.$set(this, 'generatedImage', fullData.generated_image_urls[0]);
									} else if (fullData.generated_image_url) {
										// 检查是否是相同的图片URL
										const isSameImage = this.generatedImage === fullData.generated_image_url;

										this.$set(this, 'generatedImage', fullData.generated_image_url);

										// 只有在图片URL发生变化时才重置加载状态
										if (!isSameImage) {
											this.$set(this, 'singleImageLoaded', false);
										}
									}

									// 强制更新视图
									this.$forceUpdate();
								}
							} catch (err) {
								console.error('获取完整生成记录失败:', err);
								// 错误时继续使用当前数据
							}
						}
					} else if (result.status === 'FAILED') {
						// 生成失败
						this.isGenerating = false;
						this.generationInProgress = false;
						this.generationError = result.error_message || '生成失败，请联系客服';
						clearInterval(this.generationCheckTimer);
						this.generationCheckTimer = null;
						uni.hideLoading();
						uni.showToast({
							title: this.generationError,
							icon: 'none',
							duration: 3000
						});
						
						// 显示失败状态
						this.scrollToResult();
					} else if (result.status === 'PARTIAL') {
						// 部分成功 - 先更新状态标志
						this.isGenerating = false;
						this.generationInProgress = false;
						clearInterval(this.generationCheckTimer);
						this.generationCheckTimer = null;
						uni.hideLoading();

						// 然后处理生成结果
						this.handleGenerationSuccess(result);

						// 提示部分成功
						uni.showToast({
							title: `已成功生成${(result.generated_image_urls || []).length}张图片，部分图片生成失败`,
							icon: 'none',
							duration: 3000
						});

						this.scrollToResult();
					}
					// PENDING或PROCESSING状态继续等待
					else if (result.status === 'PENDING' || result.status === 'PROCESSING') {
						// 确保生成中状态正确设置
						this.generationInProgress = true;
						this.isGenerating = true;
					}
					
				} catch (e) {
					console.error('检查生成状态失败:', e);
					// 出错不中断轮询
				}
			},
			
			// 开始定时检查生成状态
			startStatusCheck() {
				// 清除可能存在的旧定时器
				if (this.generationCheckTimer) {
					clearInterval(this.generationCheckTimer);
				}
				
				// 每3秒检查一次状态
				this.generationCheckTimer = setInterval(() => {
					this.checkGenerationStatus();
				}, 3000);
			},
			
			async generateImage() {
				// 生成前验证：如有自定义字段但未输入值，阻止提交
				for (const [key, val] of Object.entries(this.variableValues)) {
					if (val === 'custom' && this.showCustomFields[key]) {
						const inputVal = this.customValues[key] || '';
						if (!inputVal.trim()) {
							uni.showToast({
								title: this.getCustomPlaceholder(this.styleInfo.template_variables.find(v=>v.name===key)) || '请输入内容',
								icon: 'none'
							});
							return; // 中断生成
						}
					}
				}

				// 特别验证：对于自由创作风格，确保有描述内容
				if (this.styleInfo && this.styleInfo.template_type === 'VARIABLE_PROMPT') {
					const detailVariable = this.styleInfo.template_variables?.find(v => v.name === 'detail');
					if (detailVariable && (!this.styleInfo.prompt_template || this.styleInfo.prompt_template.trim() === '')) {
						// 这是自由创作风格，检查是否有描述内容
						const detailValue = this.variableValues['detail'] || this.prompt || '';
						if (!detailValue.trim()) {
							uni.showToast({
								title: '请输入描述内容',
								icon: 'none'
							});
							return; // 中断生成
						}
					}
				}
				if(this.tempImages.length === 0) {
					uni.showToast({
						title: '请先上传图片',
						icon: 'none'
					});
					return;
				}
				if (this.isGenerating) {
					uni.showToast({
						title: '正在生成中，请稍候',
						icon: 'none'
					});
					return;
				}
				
				// 检查哇图币余额是否足够
				const requiredCost = this.styleInfo ? this.styleInfo.cost : 99; // 未加载时使用默认值99
				if (this.userCoins < requiredCost) {
					uni.showModal({
						title: '哇图币不足',
						content: `生成需要${requiredCost}哇图币，您当前余额为${this.userCoins}哇图币`,
						confirmText: '去获取',
						cancelText: '取消',
						success: (res) => {
							if (res.confirm) {
								// 跳转到支付页面获取哇图币
								uni.navigateTo({
									url: '/pages/pay/pay'
								});
							}
						}
					});
					return;
				}

				// 确保已获取订阅消息模板ID
				if (!this.subscriptionTemplateId) {
					try {
						await this.fetchSubscribeTemplateId();
					} catch (e) {
						console.error('获取订阅消息模板ID失败:', e);
					}
				}

				// 先请求订阅消息权限（仅当有模板ID时）
				try {
					// 使用小程序的订阅消息API
					if (this.subscriptionTemplateId) {
						await new Promise((resolve) => {
							uni.requestSubscribeMessage({
								tmplIds: [this.subscriptionTemplateId],
								success: (res) => {
									console.log('订阅消息请求结果:', res);
									// 记录用户是否同意接收通知，但无论如何都继续生成流程
									if (res[this.subscriptionTemplateId] === 'accept') {
										console.log('用户同意接收通知');
										// 可以在这里将用户的订阅状态存储到本地或服务器
									} else {
										console.log('用户拒绝接收通知或请求失败');
									}
									resolve();
								},
								fail: (err) => {
									console.error('订阅消息请求失败:', err);
									// 失败也继续生成流程
									resolve();
								}
							});
						});
					}
				} catch (subscribeErr) {
					console.error('订阅消息异常:', subscribeErr);
					// 出现异常也继续生成流程
				}
				
				// 进入新一轮生成，重置历史标记，防止后续状态回调覆盖为旧数据
				this.isFromHistory = false;
				this.currentGenerationId = null;

				// 清除之前的生成结果，确保显示生成中状态
				this.$set(this, 'generatedImage', '');
				this.$set(this, 'generatedImages', []);
				this.$set(this, 'generationError', '');

				// 重置图片加载状态
				this.$set(this, 'imageLoadedStatus', []);
				this.$set(this, 'singleImageLoaded', false);

				// 设置生成状态
				this.isGenerating = true;
				this.generationInProgress = true;
				
				uni.showLoading({
					title: '正在上传图片...'
				});

				try {
					// 上传所有图片到后端，获取URL数组
					const uploadedUrls = await this.uploadImage();
					
					if(uploadedUrls.length === 0) {
						throw new Error('图片上传失败');
					}
					
					uni.showLoading({
						title: '正在提交任务...'
					});

					// 统一在请求体中传递ratio参数
					const selectedRatio = this.aspectRatios[this.selectedRatioIndex].value;
					let reqBody = {
						source_image_urls: uploadedUrls,
						ratio: selectedRatio  // 统一在请求体中传递ratio
					};

					if (this.styleInfo && this.styleInfo.template_type === 'VARIABLE_PROMPT') {
						// 处理变量提示词
						const vars = {};
						if (this.styleInfo.template_variables) {
							// 处理所有定义的变量
							this.styleInfo.template_variables.forEach(variable => {
								// 获取变量的值
								let value = this.variableValues[variable.name];

								// 如果是自定义输入，且有值，则使用自定义值
								if (value === 'custom' && this.customValues[variable.name]) {
									value = this.customValues[variable.name];
								} else if (value === 'custom' && !this.customValues[variable.name]) {
									// 如果选择了自定义但未输入，使用默认值或提示用户
									uni.showToast({
										title: `请输入自定义${variable.label || variable.name}`,
										icon: 'none'
									});
									return; // 中断生成流程
								}

								// 移除在变量中嵌入ratio的逻辑，直接使用变量值
								if (variable.name === "detail") {
									// 优先使用变量值，如果没有则使用prompt，如果都没有则使用空字符串
									let detailContent = value || this.prompt || '';

									// 对于自由创作风格，如果没有任何内容，提示用户输入
									if (!detailContent.trim() && (!this.styleInfo.prompt_template || this.styleInfo.prompt_template.trim() === '')) {
										uni.showToast({
											title: '请输入描述内容',
											icon: 'none'
										});
										return; // 中断生成流程
									}

									vars[variable.name] = detailContent;  // 不再添加ratio
								} else {
									// 其他变量直接使用值
									vars[variable.name] = value;
								}
							});
						} else if (this.styleInfo.template_variables && this.styleInfo.template_variables.length === 1) {
							// 兼容原有单变量逻辑
							const key = this.styleInfo.template_variables[0].name;

							// 获取内容，优先使用变量值，然后是prompt
							let content = this.variableValues[key] || this.prompt || '';

							// 对于自由创作风格，如果没有任何内容，提示用户输入
							if (!content.trim() && (!this.styleInfo.prompt_template || this.styleInfo.prompt_template.trim() === '')) {
								uni.showToast({
									title: '请输入描述内容',
									icon: 'none'
								});
								return; // 中断生成流程
							}

							vars[key] = content;  // 不再添加ratio
						}

						reqBody.variables = vars;
					} else {
						// FULL_PROMPT 兼容旧逻辑 - 不再在prompt中添加ratio
						reqBody.prompt = this.styleInfo ? this.styleInfo.prompt_template : '';
					}
					
					const resp = await request.post(`/wowpic/generate/${this.styleId}/async`, reqBody, { 
						showLoading: false,
						timeout: 10000
					});

					// 获取任务ID并开始检查状态
					this.generationTaskId = resp.task_id;
					this.generationStatus = 'PENDING';
					
					// 显示生成中状态
					uni.hideLoading();
					uni.showToast({
						title: '已开始生成，稍后将展示结果',
						icon: 'none',
						duration: 2000
					});
					
					// 滚动到结果区域
					this.scrollToResult();
					
					// 开始定期检查状态
					this.startStatusCheck();

				} catch (error) {
					console.error('生成图片失败:', error);
					this.isGenerating = false;
					this.generationInProgress = false;
					uni.hideLoading();
					
					// 检查是否是402错误（哇图币不足）
					if (error.statusCode === 402) {
						// 由request.js中的统一处理来显示提示，这里不需要额外处理
						// 但可以重置生成状态
						this.generationStatus = null;
					} else {
						// 其他错误显示通用提示
						uni.showToast({
							title: '提交生成任务失败，请重试',
							icon: 'none',
							duration: 2000
						});
					}
				}
			},
			// 更新保存图片方法，支持多图
			saveImage() {
				// 如果有多张图片，让用户选择要保存哪张
				if (this.generatedImages && this.generatedImages.length > 1) {
					uni.showActionSheet({
						itemList: this.generatedImages.map((_, idx) => `保存第${idx + 1}张图片`),
						success: (res) => {
							const index = res.tapIndex;
							if (index >= 0 && index < this.generatedImages.length) {
								this._saveImageToAlbum(this.generatedImages[index]);
							}
						}
					});
				} else {
					// 单图直接保存
					let imageUrl = this.generatedImages && this.generatedImages.length > 0 
						? this.generatedImages[0] 
						: this.generatedImage;
					
					if (imageUrl) {
						this._saveImageToAlbum(imageUrl);
					}
				}
			},
			
			// 实际保存图片到相册
			_saveImageToAlbum(imageUrl) {
				if (!imageUrl) return;
				
				// 确保使用完整URL
				const fullUrl = this.getImageUrl(imageUrl);
				
				uni.getImageInfo({
					src: fullUrl,
					success: (image) => {
						uni.saveImageToPhotosAlbum({
							filePath: image.path,
							success: () => {
								uni.showToast({
									title: '已保存到相册',
									icon: 'success'
								});
							},
							fail: (err) => {
								console.error('保存图片失败:', err);
								// 如果是因为用户拒绝授权，则引导用户开启权限
								if (err.errMsg.indexOf('auth deny') >= 0) {
									uni.showModal({
										title: '提示',
										content: '保存图片需要相册权限，请前往设置开启',
										confirmText: '去设置',
										success: (res) => {
											if (res.confirm) {
												uni.openSetting();
											}
										}
									});
								} else {
									uni.showToast({
										title: '保存图片失败',
										icon: 'none'
									});
								}
							}
						});
					},
					fail: (err) => {
						console.error('获取图片信息失败:', err);
						uni.showToast({
							title: '保存图片失败',
							icon: 'none'
						});
					}
				});
			},
			// 准备分享 - 设置要分享的图片
			prepareShare() {
				console.log('prepareShare方法被调用');

				// 获取要分享的图片
				let imageToShare = '';
				if (this.generatedImages && this.generatedImages.length > 0) {
					imageToShare = this.generatedImages[0];
				} else if (this.generatedImage) {
					imageToShare = this.generatedImage;
				}

				if (!imageToShare) {
					uni.showToast({
						title: '暂无图片可分享',
						icon: 'none'
					});
					return;
				}

				// 设置当前分享的图片URL
				this.currentShareImageUrl = imageToShare;
			},
			// 预览图片
			previewImage(url) {
				if (!url) return;
				
				let urls = [];
				
				// 如果有多张图片，将所有图片URL添加到预览数组
				if (this.generatedImages && this.generatedImages.length > 0) {
					urls = this.generatedImages.map(img => this.getImageUrl(img));
				} else if (this.generatedImage) {
					urls = [this.getImageUrl(this.generatedImage)];
				} else {
					return;
				}
				
				// 确保使用完整URL
				const fullUrl = this.getImageUrl(url);
				
				uni.previewImage({
					urls: urls,
					current: fullUrl,
					longPressActions: {
						itemList: ['保存图片', '分享图片'],
						success: (data) => {
							// 更新当前图片索引
							this.currentImageIndex = data.index || 0;
							
							if (data.tapIndex === 0) {
								this.saveImage();
							} else if (data.tapIndex === 1) {
								this.shareImage();
							}
						}
					}
				});
			},
			// 轮播图变化时更新索引
			onSwiperChange(e) {
				this.currentImageIndex = e.detail.current;
			},
			// 新增：获取预填充提示词的方法
			async fetchPrefillPrompt(count) {
				if (!this.styleInfo || this.styleInfo.template_type !== 'VARIABLE_PROMPT') {
					return;
				}
				
				try {
					// 调用新接口
					const res = await request.post(`/wowpic/generate/${this.styleId}/prefill`, {
						image_count: count
					}, { showLoading: false });

					// 处理不同类型变量的预填充
					if (res && res.prefill) {
						// 找出变量类型
						const singleVariableMode = this.styleInfo.template_variables && 
												  this.styleInfo.template_variables.length === 1;
						
						if (singleVariableMode) {
							const variableName = this.styleInfo.template_variables[0].name;
							const variableType = this.styleInfo.template_variables[0].type;
							
							// 处理不同类型的变量
							if (variableType === 'select') {
								// 如果是选择类型变量，尝试匹配选项
								const options = this.styleInfo.template_variables[0].options || [];
								const matchedOption = options.find(opt => opt.value === res.prefill);
								
								if (matchedOption) {
									this.variableValues[variableName] = matchedOption.value;
									this.showCustomFields[variableName] = !!matchedOption.custom_field;
								}
							} else if (variableName === 'detail') {
								// 特殊处理detail类型（兼容旧版）
						if (this.prompt === '' || this.prompt === this.lastPrefill) {
							this.prompt = res.prefill;
						}
						// 更新"上次预填充内容"的记录
						this.lastPrefill = res.prefill;
							}
						} else {
							// detail变量的处理，保持原有逻辑
							if (this.prompt === '' || this.prompt === this.lastPrefill) {
								this.prompt = res.prefill;
							}
							// 更新"上次预填充内容"的记录
							this.lastPrefill = res.prefill;
						}
					}
				} catch (error) {
					console.error('获取预填充提示词失败', error);
				}
			},
			// 获取订阅消息模板ID
			async fetchSubscribeTemplateId() {
				try {
					const res = await request.get('/wowpic/notify/template-id', {}, { hideErrorTips: true });
					if (res && res.templateId) {
						this.subscriptionTemplateId = res.templateId;
					}
				} catch (e) {
					console.error('获取订阅模板ID失败', e);
				}
			},

			// 加载风格相关公告
			async loadStyleNotice() {
				if (!this.styleInfo || !this.styleInfo.model_identifier) {
					return;
				}

				try {
					const res = await request.get('/wowpic/notices', {
						display_type: 'style_page',
						style_identifier: this.styleInfo.model_identifier
					}, { showLoading: false });

					console.log('获取风格公告结果:', res);

					if (res && res.has_notice && res.notice) {
						const noticeId = res.notice.id;

						// 获取应用全局数据，用于存储会话级别的已显示公告
						const app = getApp();
						if (!app.globalData.shownNotices) {
							app.globalData.shownNotices = [];
						}

						// 检查是否已经在当前会话中显示过
						if (!app.globalData.shownNotices.includes(noticeId)) {
							this.noticeData = res.notice;
							this.showNotice = true;
							app.globalData.shownNotices.push(noticeId);
						}
					}
				} catch (err) {
					console.error('获取风格公告失败', err);
				}
			},

			// 关闭公告
			closeNotice() {
				this.showNotice = false;
				// 注意：这里不使用本地存储，只在会话中记录，确保每次进入应用都能看到公告
			},
			// 处理图片加载完成和失败的统一处理函数
			onSingleImageLoaded() {
				this.singleImageLoaded = true;
			},
			
			onSingleImageLoadError() {
				this.singleImageLoaded = true; // 即使错误也显示，避免永久loading
				uni.showToast({
					title: '图片加载失败',
					icon: 'none'
				});
			},
			
			onImageLoaded(index) {
				// 使用Vue的$set方法确保响应式更新
				this.$set(this.imageLoadedStatus, index, true);
				// 确保视图更新
				this.$nextTick(() => {
					this.$forceUpdate();
				});
			},
			
			onImageLoadError(index) {
				this.$set(this.imageLoadedStatus, index, true); // 即使错误也显示
				uni.showToast({
					title: `第${index+1}张图片加载失败`,
					icon: 'none'
				});
			},
			
			// 变量和样式选择相关函数
			selectVariableOption(variableName, value) {
				this.variableValues[variableName] = value; // 直接使用选项值
				// 关闭自定义输入框
				this.showCustomFields[variableName] = false;
			},
			
			selectCustomOption(variableName) {
				this.variableValues[variableName] = 'custom';
				this.showCustomFields[variableName] = true;
			},
			
			hasCustomOption(variable) {
				return variable.options && variable.options.some(opt => opt.custom_field);
			},
			
			isCustomSelected(variableName) {
				return this.variableValues[variableName] === 'custom';
			},
			getCustomPlaceholder(variable) {
				// 先从选项定义中查找custom_placeholder属性
				const customOption = variable.options?.find(opt => opt.custom_field);
				if (customOption && customOption.custom_placeholder) {
					return customOption.custom_placeholder;
				}
				
				// 根据风格模型标识符返回特定的placeholder
				const modelIdentifier = this.styleInfo?.model_identifier;
				
				// 针对特定风格和变量名进行定制
				if (modelIdentifier === 'pet_id_photo_v1' && variable.name === 'background_style') {
					return '请输入自定义背景，如：红底、渐变底等';
				}
				
				if (modelIdentifier === 'celebrity_selfie_v1' && variable.name === 'detail') {
					return '请输入想要与哪位明星合影，如：周杰伦、刘德华等';
				}
				
				// 默认情况
				return `请输入自定义${variable.label || variable.name}`;
			},
			
			// 切换风格 - 处理从StyleSelector组件接收的风格选择
			async switchStyle(style) {
				if (this.styleId === style.id) {
					this.showStyleSelector = false;
					return;
				}
				
				try {
					// 更新风格ID和名称
					this.styleId = style.id;
					this.currentStyle = style.name;
					
					// 隐藏选择器
					this.showStyleSelector = false;
					
					// 显示加载提示
					uni.showLoading({ title: '切换风格中...' });
					
					// 重置状态
					this.prompt = '';
					this.lastPrefill = '';
					this.generatedImage = '';
					this.generatedImages = [];
					this.generationError = '';
					this.generationInProgress = false;

					// 重置图片加载状态
					this.imageLoadedStatus = [];
					this.singleImageLoaded = false;

					// 重置变量值
					this.variableValues = {};
					this.showCustomFields = {};
					this.customValues = {};
					
					// 加载新风格信息
					await this.loadStyleInfo();
					
					// 若已上传的图片数量超过新风格允许的最大数量，自动截取
					if (this.tempImages.length > this.maxImagesCount) {
						// 截断多余的本地临时图片及已上传URL
						this.tempImages = this.tempImages.slice(0, this.maxImagesCount);
						this.uploadedImageUrls = this.uploadedImageUrls.slice(0, this.maxImagesCount);
					}
					
					// 根据新风格获取预填充
					if (this.tempImages.length > 0) {
						this.fetchPrefillPrompt(this.tempImages.length);
					}
					
					uni.hideLoading();
					
					// 成功提示
					uni.showToast({
						title: '切换成功',
						icon: 'success'
					});
				} catch (err) {
					console.error('切换风格失败:', err);
					uni.hideLoading();
					uni.showToast({
						title: '切换风格失败',
						icon: 'none'
					});
				}
			},
			// 处理生成成功的结果
			handleGenerationSuccess(result) {
				if (result.generated_image_urls && result.generated_image_urls.length > 0) {
					// 检查是否是相同的图片URL，避免重复重置加载状态
					const isSameImages = this.generatedImages &&
						this.generatedImages.length === result.generated_image_urls.length &&
						this.generatedImages.every((url, index) => url === result.generated_image_urls[index]);

					// 使用Vue.set确保响应式更新
					this.$set(this, 'generatedImages', result.generated_image_urls);

					// 只有在图片URL发生变化时才重置加载状态
					if (!isSameImages) {
						this.$set(this, 'imageLoadedStatus', new Array(result.generated_image_urls.length).fill(false));
					}

					// 为了兼容，也设置第一张为generatedImage
					this.$set(this, 'generatedImage', result.generated_image_urls[0]);
				} else if (result.generated_image_url) {
					// 检查是否是相同的图片URL
					const isSameImage = this.generatedImage === result.generated_image_url;

					// 兼容旧版单图
					this.$set(this, 'generatedImage', result.generated_image_url);

					// 只有在图片URL发生变化时才重置加载状态
					if (!isSameImage) {
						this.$set(this, 'singleImageLoaded', false);
					}
				}
			},

		},
		
		// 新增：监听器
		watch: {
			// 监听生成图片数组的变化
			generatedImages: {
				handler(newImages) {
					// 如果图片数组发生变化，确保加载状态数组的长度匹配
					if (newImages && newImages.length > 0) {
						// 如果加载状态数组不存在或长度不匹配，初始化它
						if (!this.imageLoadedStatus || this.imageLoadedStatus.length !== newImages.length) {
							this.$set(this, 'imageLoadedStatus', new Array(newImages.length).fill(false));
						}
					}
				},
				deep: true,
				immediate: true
			},

			// 监听已上传图片数组的长度变化
			'tempImages.length': function(newLength, oldLength) {
				// 只有在长度确实发生变化时才调用，避免不必要请求
				if (newLength !== oldLength) {
					this.fetchPrefillPrompt(newLength);
				}
			}
		}
	}
</script>

<style>
	@import url('/static/styles/icons.css');
	
	/* 原有样式保持不变 */
	
	/* 风格选择器样式 - 移除，现在由组件提供 */
	.style-info {
		flex: 1;
		display: flex;
		align-items: center;
		background: linear-gradient(135deg, #4A90E2 0%, #7562FF 100%);
		border-radius: 35rpx;
		padding: 16rpx 30rpx;
		box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.15);
		border: 6rpx solid #333;
		position: relative;
		overflow: hidden;
	}
	
	/* 添加下拉指示图标 */
	.down-icon {
		width: 24rpx;
		height: 24rpx;
		margin-left: 10rpx;
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23FFFFFF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
	}
	

	
	.generate-container {
		height: 100vh;
		background-color: #F8F8F8;
		padding: 0 30rpx 30rpx;
	}
	
	/* 安全区域 */
	.safe-area {
		height: 88rpx;
	}
	
	/* 头部占位符，为固定头部留出空间 */
	.header-placeholder {
		height: 88rpx; /* 与style-header高度一致 */
	}
	
	/* 当前风格提示 */
	.style-header {
		display: flex;
		align-items: center;
		margin-bottom: 40rpx;
	}
	
	/* 固定头部样式 */
	.fixed-header {
		position: fixed;
		top: 88rpx; /* 安全区域高度 */
		left: 30rpx;
		right: 30rpx;
		z-index: 100;
		padding: 0 0 20rpx 0;
	}
	
	.back-btn {
		width: 70rpx;
		height: 70rpx;
		border-radius: 35rpx;
		background-color: #FFFFFF;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-right: 20rpx;
		/* 增强阴影效果，使按钮在各种背景上更加明显 */
		box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.25), 0 0 6rpx rgba(0, 0, 0, 0.1);
		/* 添加细微的边框增强对比度 */
		border: 1rpx solid rgba(0, 0, 0, 0.05);
		/* 添加过渡效果使点击更流畅 */
		transition: transform 0.2s ease, box-shadow 0.2s ease;
	}
	
	.back-btn:active {
		transform: scale(0.95);
		box-shadow: 0 3rpx 8rpx rgba(0, 0, 0, 0.2);
	}
	
	.back-icon {
		width: 36rpx;
		height: 36rpx;
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23333333' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M15 18l-6-6 6-6'/%3E%3C/svg%3E");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
	}
	
	/* 添加装饰元素 */
	.style-info::before {
		content: "";
		position: absolute;
		top: -10rpx;
		right: -10rpx;
		width: 80rpx;
		height: 80rpx;
		background: rgba(255, 255, 255, 0.2);
		border-radius: 50%;
		z-index: 0;
	}
	
	.style-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 16rpx;
		position: relative;
		z-index: 1;
	}
	
	.style-text {
		font-size: 30rpx;
		font-weight: bold;
		color: #FFFFFF;
		text-shadow: 2rpx 2rpx 0 rgba(0, 0, 0, 0.3);
		position: relative;
		z-index: 1;
	}
	
	/* 标题样式 */
	.section-title-bar {
		margin-bottom: 20rpx;
	}
	
	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-right: 16rpx;
	}
	
	.section-subtitle {
		font-size: 24rpx;
		color: #999;
	}
	
	/* 上传图片区域 */
	.upload-section {
		margin-bottom: 40rpx;
	}
	
	.multi-upload-container {
		margin-bottom: 20rpx;
		min-height: 300rpx; /* 确保容器高度一致 */
	}
	
	.uploaded-images-scroll {
		width: 100%;
		white-space: nowrap;
		margin-bottom: 0; /* 由外层 multi-upload-container 统一控制间距 */
		height: 300rpx; /* 与empty状态一致 */
	}
	
	.uploaded-images-list {
		display: inline-flex;
		padding: 10rpx 0;
		height: 300rpx; /* 与empty状态一致的高度 */
		align-items: center;
	}
	
	.image-item {
		width: 275rpx;
		height: 275rpx;
		margin-right: 16rpx;
		position: relative;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
		background-color: #FFFFFF;
	}
	
	.image-item.add-more {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		border: 2rpx dashed #4A90E2;
		background-color: #F8F9FA;
	}
	
	.image-overlay {
		position: absolute;
		top: 0;
		right: 0;
		background-color: rgba(0, 0, 0, 0.5);
		width: 48rpx;
		height: 48rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		border-bottom-left-radius: 16rpx;
	}
	
	.delete-icon {
		width: 32rpx;
		height: 32rpx;
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23FFFFFF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='3 6 5 6 21 6'%3E%3C/polyline%3E%3Cpath d='M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2'%3E%3C/path%3E%3C/svg%3E");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
	}
	
	.image-index {
		position: absolute;
		bottom: 0;
		left: 0;
		background: linear-gradient(135deg, #4A90E2 0%, #7562FF 100%);
		color: #FFFFFF;
		padding: 4rpx 16rpx;
		font-size: 24rpx;
		font-weight: bold;
		border-top-right-radius: 16rpx;
	}
	
	.preview-image {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
	
	.upload-icon.large {
		width: 120rpx;
		height: 120rpx;
	}
	
	.add-text {
		font-size: 24rpx;
		color: #4A90E2;
		margin-top: 10rpx;
	}
	
	/* 空状态上传框 */
	.upload-box.empty {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		box-sizing: border-box; /* 确保高度包含边框，消除边框宽度引起的跳动 */
		height: 300rpx;
		background-color: #F8F9FA;
		border: 4rpx dashed #4A90E2;
		border-radius: 24rpx;
		box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.08);
	}
	
	/* 提示词区域 */
	.prompt-section {
		margin-bottom: 20rpx;
	}
	
	/* 提示框样式 - 用于FULL_PROMPT类型 */
	.prompt-tip-box {
		background-color: #FFFFFF;
		border-radius: 24rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08);
		position: relative;
		display: flex;
		align-items: center;
	}
	
	.tip-icon {
		width: 80rpx;
		height: 80rpx;
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234A90E2' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpath d='M12 16v-4'%3E%3C/path%3E%3Cpath d='M12 8h.01'%3E%3C/path%3E%3C/svg%3E");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
		margin-right: 20rpx;
		flex-shrink: 0;
	}
	
	.tip-content {
		flex: 1;
	}
	
	.tip-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 8rpx;
		display: block;
	}
	
	.tip-desc {
		font-size: 26rpx;
		color: #666;
		line-height: 1.5;
	}
	
	.prompt-input-box {
		background-color: #FFFFFF;
		border-radius: 24rpx;
		padding: 24rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08);
		position: relative;
		/* 不使用边框，用阴影提供边界感 */
	}
	
	.prompt-input {
		width: 100%;
		height: 160rpx;
		font-size: 28rpx;
		color: #333;
		line-height: 1.5;
	}
	
	.prompt-length {
		position: absolute;
		bottom: 16rpx;
		right: 24rpx;
		font-size: 24rpx;
		color: #999;
	}
	
	.prompt-tags {
		display: flex;
		flex-wrap: wrap;
		gap: 16rpx;
	}
	
	.tag {
		padding: 12rpx 24rpx;
		background-color: #FFFFFF;
		border-radius: 30rpx;
		font-size: 24rpx;
		color: #7562FF;
		/* 使用品牌紫色作为边框，保持轻量感 */
		border: 2rpx solid #7562FF;
		transition: all 0.3s ease;
		display: flex;
		align-items: center;
		box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	
	.tag::before {
		content: "";
		display: inline-block;
		width: 20rpx;
		height: 20rpx;
		margin-right: 8rpx;
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%237562FF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z'%3E%3C/path%3E%3Cline x1='7' y1='7' x2='7.01' y2='7'%3E%3C/line%3E%3C/svg%3E");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
		vertical-align: middle;
	}
	
	.tag:active {
		background-color: #7562FF;
		color: #FFFFFF;
		transform: scale(0.95);
	}
	
	.tag:active::before {
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23FFFFFF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z'%3E%3C/path%3E%3Cline x1='7' y1='7' x2='7.01' y2='7'%3E%3C/line%3E%3C/svg%3E");
	}
	
	/* 比例选择样式 - 简化版 */
	.aspect-ratio-options {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 16rpx;
		padding: 16rpx 0;
		height: 80rpx; /* 固定高度，防止选中时布局跳动 */
	}
	
	.aspect-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 100%;
		border-radius: 16rpx;
		background-color: #F8F8F8;
		border: 3rpx solid #cecece;
		transition: all 0.3s ease;
		flex: 1;
		margin: 0 8rpx;
		box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
		position: relative;
		overflow: hidden;
	}
	
	.aspect-btn:first-child {
		margin-left: 0;
	}
	
	.aspect-btn:last-child {
		margin-right: 0;
	}
	
	.aspect-btn.active {
		background-color: #FFFFFF;
		border-color: #7562FF;
		box-shadow: 0 6rpx 12rpx rgba(117, 98, 255, 0.2);
	}
	
	.aspect-icon {
		width: 36rpx;
		height: 36rpx;
		margin-right: 12rpx;
		position: relative;
		border: 3rpx solid #555;
		border-radius: 6rpx;
	}
	
	.aspect-btn.active .aspect-icon {
		border-color: #7562FF;
		border-width: 4rpx;
	}
	
	/* 正方形图标 */
	.aspect-icon.square {
		width: 32rpx;
		height: 32rpx;
	}
	
	/* 竖屏图标 */
	.aspect-icon.portrait {
		width: 26rpx;
		height: 38rpx;
	}
	
	/* 横屏图标 */
	.aspect-icon.landscape {
		width: 38rpx;
		height: 26rpx;
	}
	
	.aspect-btn-text {
		font-size: 28rpx;
		color: #333;
		font-weight: 500;
	}
	
	.aspect-btn.active .aspect-btn-text {
		color: #7562FF;
		font-weight: bold;
	}
	
	/* 底部操作区 */
	/* 底部操作区 - 完全重新设计，区别于个人信息卡片 */
	.bottom-action {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 40rpx;
		/* 改用白色背景 */
		background-color: #FFFFFF;
		border-radius: 24rpx;
		padding: 28rpx 24rpx;
		box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
		/* 不使用黑色边框，改用渐变边框效果 */
		position: relative;
	}
	
	/* 渐变边框效果 */
	.bottom-action::before {
		content: "";
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		border-radius: 24rpx;
		padding: 4rpx;
		background: linear-gradient(135deg, #4A90E2 0%, #7562FF 100%);
		-webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
		-webkit-mask-composite: destination-out;
		mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
		mask-composite: exclude;
		pointer-events: none;
	}
	
	.coin-info {
		display: flex;
		align-items: center;
		/* 使用轻微的背景色 */
		background-color: #F2F6FF;
		padding: 12rpx 20rpx;
		border-radius: 30rpx;
		box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	
	.coin-icon {
		width: 60rpx;
		height: 60rpx;
		margin-right: 12rpx;
	}
	
	.coin-text {
		font-size: 28rpx;
		font-weight: bold;
		color: #7562FF;
		/* 不使用文字阴影，保持清爽 */
	}
	
	/* 根据客户喜好保留黑色边框的按钮 */
	.generate-btn {
		background-color: #FFFFFF;
		color: #333333;
		font-size: 30rpx;
		font-weight: bold;
		padding: 16rpx 40rpx;
		border-radius: 30rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: transform 0.3s ease;
		border: 4rpx solid #333;
		box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
		position: relative;
		z-index: 1;
	}

	.generate-btn text {
		line-height: 1;
		display: flex;
		align-items: center;
	}
	
	.generate-btn:active {
		transform: scale(0.95);
	}
	
	.generate-btn.disabled {
		background-color: #CCCCCC;
		opacity: 0.7;
		border-color: #999999;
	}
	
	.btn-icon {
		margin-left: 10rpx;
		width: 28rpx;
		height: 28rpx;
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23333333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
		flex-shrink: 0;
		align-self: center;
	}
	
	/* 结果区域 */
	.result-section {
		background-color: #FFFFFF;
		border-radius: 24rpx;
		padding: 30rpx;
		margin-top: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
		/* 不使用黑色边框，保持轻量感 */
	}
	
	.result-box {
		border-radius: 16rpx;
		overflow: hidden;
		margin-bottom: 30rpx;
		position: relative;
		box-shadow: 0 12rpx 24rpx rgba(0, 0, 0, 0.1);
	}
	
	.result-image {
		width: 100%;
		display: block;
		border-radius: 16rpx;
	}

	.result-images-list {
		display: flex;
		flex-direction: column;
		gap: 16rpx; /* 图片之间的间距 */
	}

	.result-image-item {
		width: 100%;
	}
	
	.action-row {
		display: flex;
		justify-content: space-between;
		margin-top: 30rpx;
		margin-bottom: 20rpx;
	}
	
	.action-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 16rpx 24rpx;
		border-radius: 30rpx;
		flex: 1;
		transition: transform 0.3s ease;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}
	
	.action-btn:active {
		transform: scale(0.95);
	}
	
	.save-btn {
		background-color: #F8F8F8;
		margin-right: 20rpx;
		color: #333;
		border: 1rpx solid #E0E0E0;
	}
	
	.share-btn {
		background: linear-gradient(135deg, #4A90E2 0%, #7562FF 100%);
		color: #FFFFFF;
		/* 重置button默认样式 */
		padding: 0;
		margin: 0;
		line-height: normal;
		border: none;
	}

	.share-btn::after {
		border: none;
	}
	
	.action-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 12rpx;
	}
	
	.save-icon {
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23333333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z'/%3E%3Cpolyline points='17 21 17 13 7 13 7 21'/%3E%3Cpolyline points='7 3 7 8 15 8'/%3E%3C/svg%3E");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
	}
	
	.share-icon {
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23FFFFFF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='18' cy='5' r='3'/%3E%3Ccircle cx='6' cy='12' r='3'/%3E%3Ccircle cx='18' cy='19' r='3'/%3E%3Cline x1='8.59' y1='13.51' x2='15.42' y2='17.49'/%3E%3Cline x1='15.41' y1='6.51' x2='8.59' y2='10.49'/%3E%3C/svg%3E");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
	}
	
	.action-btn text {
		font-size: 28rpx;
		font-weight: 500;
	}
	
	.share-btn text {
		color: #FFFFFF;
		text-shadow: 1rpx 1rpx 0 rgba(0, 0, 0, 0.2);
	}
	

	/* 生成中状态样式 */
	.generating-status {
		width: 100%;
		padding: 0;
		width: 500rpx;
		height: 500rpx;
		margin: 20rpx auto 20rpx auto;
		background-color: #f8f9fa;
		border-radius: 16rpx;
		text-align: center;
		box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
		border: 4rpx solid rgba(117, 98, 255, 0.2);
		position: relative;
		overflow: hidden;
		background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
		background-size: 800px 100%;
		animation: shimmer 2s infinite linear;
	}

	/* 添加装饰元素，增强视觉效果 */
	.generating-status::after {
		content: "";
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 120rpx;
		height: 120rpx;
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%237562FF' stroke-width='1' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='2' y='2' width='20' height='20' rx='5' ry='5'%3E%3C/rect%3E%3Cpath d='M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z'%3E%3C/path%3E%3Cline x1='17.5' y1='6.5' x2='17.51' y2='6.5'%3E%3C/line%3E%3C/svg%3E");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
		opacity: 0.2;
	}

	@keyframes shimmer {
		0% {
			background-position: -800px 0;
		}
		100% {
			background-position: 800px 0;
		}
	}

	.status-tip {
		font-size: 24rpx;
		color: #999;
		text-align: center;
		margin-bottom: 30rpx;
		/* 添加淡入效果 */
		animation: fadeIn 0.5s ease-in;
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
		}
		to {
			opacity: 1;
		}
	}

	/* 等待生成状态样式 */
	.waiting-status {
		width: 100%;
		padding: 40rpx 0;
		background-color: #f8f9fa;
		border-radius: 24rpx;
		text-align: center;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
		border: 2rpx dashed #ccc;
	}
	
	.waiting-icon {
		width: 100rpx;
		height: 100rpx;
		margin: 0 auto 20rpx;
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%237562FF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpath d='M8 14s1.5 2 4 2 4-2 4-2'%3E%3C/path%3E%3Cline x1='9' y1='9' x2='9.01' y2='9'%3E%3C/line%3E%3Cline x1='15' y1='9' x2='15.01' y2='9'%3E%3C/line%3E%3C/svg%3E");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
	}
	
	.waiting-text {
		font-size: 28rpx;
		color: #7562FF;
		padding: 0 40rpx;
		line-height: 1.5;
	}
	
	/* 生成失败状态样式 */
	.error-status {
		width: 100%;
		padding: 40rpx 0;
		background-color: #f8f9fa;
		border-radius: 24rpx;
		text-align: center;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
		border: 2rpx dashed #ccc;
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.error-icon {
		width: 100rpx;
		height: 100rpx;
		margin: 0 auto 20rpx;
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23FF6B6B' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='15' y1='9' x2='9' y2='15'%3E%3C/line%3E%3Cline x1='9' y1='9' x2='15' y2='15'%3E%3C/line%3E%3C/svg%3E");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
	}
	
	.error-content {
		flex: 1;
	}
	
	.error-text {
		font-size: 28rpx;
		color: #FF6B6B;
		padding: 0 40rpx;
		line-height: 1.5;
		margin-bottom: 10rpx;
		font-weight: bold;
	}
	
	.retry-btn {
		background: linear-gradient(135deg, #4A90E2 0%, #7562FF 100%);
		color: #FFFFFF;
		padding: 12rpx 24rpx;
		border-radius: 30rpx;
		font-size: 28rpx;
		font-weight: bold;
		margin-top: 20rpx;
		transition: transform 0.3s ease;
	}
	
	.retry-btn:active {
		transform: scale(0.95);
	}


	
	/* 新增：图片加载动画样式 */
	.image-loading-container {
		width: 100%;
		padding-bottom: 100%; /* 保持1:1比例的占位 */
		position: relative;
		background-color: #f0f0f0;
		border-radius: 16rpx;
		overflow: hidden;
	}
	
	.image-loading-animation {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
		background-size: 200% 100%;
		animation: shimmer 1.5s infinite linear;
	}
	
	.image-loading-animation::after {
		content: "";
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 80rpx;
		height: 80rpx;
		border: 6rpx solid #7562FF;
		border-top-color: transparent;
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}
	
	.image-container {
		width: 100%;
		position: relative;
		border-radius: 16rpx;
		overflow: hidden;
	}
	
	.hidden {
		opacity: 0;
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
	}
	
	.result-image.hidden {
		opacity: 0;
	}
	
	.result-image:not(.hidden) {
		opacity: 1;
		transition: opacity 0.3s ease;
	}
	
	@keyframes spin {
		0% { transform: translate(-50%, -50%) rotate(0deg); }
		100% { transform: translate(-50%, -50%) rotate(360deg); }
	}
	
	/* 宠物证件照背景样式选择器 */
	.style-options-box {
		margin-bottom: 20rpx;
		padding: 20rpx;
		background-color: #FFFFFF;
		border-radius: 16rpx;
		box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.08);
	}
	
	.style-options-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 16rpx;
	}
	
	.style-options-container {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	
	.style-option-btn {
		width: 30%;
		height: 80rpx;
		border-radius: 16rpx;
		background-color: #F8F8F8;
		border: 2rpx solid #E0E0E0;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		transition: all 0.3s ease;
	}
	
	.style-option-btn.active {
		border-color: #7562FF;
		background-color: #7562FF;
		color: #FFFFFF;
	}
	
	.style-option-icon {
		width: 40rpx;
		height: 40rpx;
		margin-bottom: 8rpx;
		border-radius: 50%;
	}
	
	.white-bg {
		background-color: #FFFFFF;
		border: 2rpx solid #E0E0E0;
	}
	
	.blue-bg {
		background-color: #4A90E2;
		border: 2rpx solid #4A90E2;
	}
	
	.custom-bg {
		background: linear-gradient(135deg, #F7A8BE, #D193C8);
		border: 2rpx solid #D193C8;
	}
	
	.style-option-btn.active .style-option-text {
		color: #FFFFFF;
	}
	
	.style-option-text {
		font-size: 24rpx;
		color: #333;
	}
	
	.custom-style-input {
		margin-top: 20rpx;
		padding: 16rpx;
		background-color: #FFFFFF;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.08);
	}
	
	.input-field {
		width: 100%;
		height: 120rpx;
		font-size: 28rpx;
		color: #333;
		line-height: 1.5;
	}
	
	/* 通用变量选择器 - 适用于任何VARIABLE_PROMPT类型风格 */
	.variables-container {
		margin-bottom: 20rpx;
		background-color: #FFFFFF;
		border-radius: 16rpx;
		padding: 24rpx;
		box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.08);
	}
	
	.variable-item {
		margin-bottom: 24rpx;
	}
	
	.variable-item:last-child {
		margin-bottom: 0;
	}
	
	.variable-selector-box {
		margin-bottom: 20rpx;
		padding: 24rpx;
		background: linear-gradient(135deg, #FFFFFF 0%, #F8FAFF 100%);
		border-radius: 20rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
		border: 2rpx solid #F0F4FF;
		position: relative;
		overflow: hidden;
		min-height: 120rpx; /* 设置最小高度确保容器稳定 */
	}



	.variable-label {
		font-size: 30rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 20rpx;
		display: block;
		position: relative;
		padding-left: 16rpx;
	}

	.variable-label::before {
		content: "";
		position: absolute;
		left: 0;
		top: 50%;
		transform: translateY(-50%);
		width: 6rpx;
		height: 20rpx;
		background: linear-gradient(135deg, #7562FF 0%, #9C88FF 100%);
		border-radius: 3rpx;
	}
	
	.select-options {
		display: flex;
		flex-wrap: wrap;
		gap: 16rpx;
		margin-top: 20rpx;
		animation: fadeInUp 0.4s ease;
	}

	@keyframes fadeInUp {
		0% {
			opacity: 0;
			transform: translateY(20rpx);
		}
		100% {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.option-btn {
		flex: 1;
		min-width: 140rpx;
		padding: 20rpx 28rpx;
		background: linear-gradient(135deg, #FFFFFF 0%, #F8FAFF 100%);
		border-radius: 16rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		position: relative;
		border: 2rpx solid #E8EEFF;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
		overflow: hidden;
	}

	.option-btn::before {
		content: "";
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, #7562FF 0%, #9C88FF 100%);
		opacity: 0;
		transition: opacity 0.3s ease;
		z-index: 0;
	}

	.option-btn:hover {
		transform: translateY(-2rpx);
		box-shadow: 0 6rpx 20rpx rgba(117, 98, 255, 0.15);
		border-color: #7562FF;
	}

	.option-btn.active {
		background: linear-gradient(135deg, #7562FF 0%, #9C88FF 100%);
		border-color: #7562FF;
		box-shadow: 0 8rpx 24rpx rgba(117, 98, 255, 0.3);
		transform: translateY(-2rpx);
	}

	.option-btn.active::before {
		opacity: 1;
	}

	.option-btn.active::after {
		content: "✓";
		position: absolute;
		top: 8rpx;
		right: 8rpx;
		width: 24rpx;
		height: 24rpx;
		background-color: rgba(255, 255, 255, 0.2);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 16rpx;
		color: #FFFFFF;
		font-weight: bold;
		z-index: 2;
	}

	.option-text {
		font-size: 28rpx;
		font-weight: 500;
		color: #333333;
		text-align: center;
		position: relative;
		z-index: 1;
		transition: all 0.3s ease;
	}

	.option-btn.active .option-text {
		color: #FFFFFF;
		font-weight: 600;
		text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
	}
	
	.option-custom {
		width: 100%;
		margin-top: 0; /* 移除上边距 */
		display: flex;
		flex-direction: row;
		align-items: stretch;
		gap: 0;
		background: linear-gradient(135deg, #F8FAFF 0%, #FFFFFF 100%);
		border-radius: 16rpx;
		padding: 4rpx;
		border: 2rpx solid #7562FF;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
		position: relative;
		overflow: hidden;
		transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
		height: 68rpx; /* 恢复固定高度，匹配option-btn的实际高度 */
	}

	.option-custom::before {
		content: "";
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, rgba(117, 98, 255, 0.05) 0%, rgba(156, 136, 255, 0.05) 100%);
		opacity: 0;
		transition: opacity 0.3s ease;
		pointer-events: none;
	}

	.option-custom-active {
		border-color: #7562FF;
		border-width: 3rpx; /* 激活状态边框稍微加粗 */
		box-shadow: 0 6rpx 24rpx rgba(117, 98, 255, 0.25);
		background: linear-gradient(135deg, #FFFFFF 0%, #F8FAFF 100%);
	}

	.option-custom-active::before {
		opacity: 1;
	}

	.custom-option {
		flex: 1;
		min-width: 0;
		transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
		position: relative;
		z-index: 2;
	}

	.option-custom-active .custom-option {
		flex: 0 0 140rpx;
		min-width: 140rpx;
	}

	.inline-input {
		background: linear-gradient(135deg, #FFFFFF 0%, #F8FAFF 100%);
		border-radius: 12rpx;
		padding: 0 20rpx; /* 只设置水平内边距，垂直居中通过flex实现 */
		font-size: 28rpx;
		border: none;
		box-shadow: none;
		color: #333;
		margin-top: 0;
		margin-left: 8rpx;
		transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
		flex: 0;
		width: 0;
		opacity: 0;
		overflow: hidden;
		transform: translateX(-30rpx) scale(0.9);
		position: relative;
		z-index: 1;
		height: 60rpx; /* 设置固定高度匹配容器内部空间 */
		line-height: 1.4; /* 优化行高 */
		display: flex;
		align-items: center; /* 确保文字垂直居中 */
	}

	.inline-input::placeholder {
		color: #999;
		transition: color 0.3s ease;
	}

	.inline-input:focus {
		outline: none;
		transform: translateX(0) scale(1);
	}

	.inline-input:focus::placeholder {
		color: #7562FF;
	}

	.option-custom-active .inline-input {
		opacity: 1;
		flex: 1;
		width: auto;
		transform: translateX(0) scale(1);
		animation: expandInput 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}

	@keyframes expandInput {
		0% {
			opacity: 0;
			transform: translateX(-30rpx) scale(0.9);
			width: 0;
			flex: 0;
		}
		30% {
			opacity: 0.3;
			transform: translateX(-15rpx) scale(0.95);
		}
		60% {
			opacity: 0.7;
			transform: translateX(-5rpx) scale(0.98);
			width: auto;
		}
		80% {
			opacity: 0.9;
			transform: translateX(2rpx) scale(1.01);
		}
		100% {
			opacity: 1;
			transform: translateX(0) scale(1);
			width: auto;
			flex: 1;
		}
	}

	/* 添加微妙的悬停效果 */
	.option-custom:hover {
		transform: translateY(-1rpx);
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
	}

	.option-custom-active:hover {
		transform: translateY(-1rpx);
		box-shadow: 0 8rpx 28rpx rgba(117, 98, 255, 0.25);
	}

	/* 响应式优化 */
	@media screen and (max-width: 750rpx) {
		.option-btn {
			min-width: 120rpx;
			padding: 18rpx 24rpx;
			font-size: 26rpx;
		}

		.variable-selector-box {
			padding: 20rpx;
			margin-bottom: 28rpx;
			min-height: 100rpx; /* 小屏设备的最小高度 */
		}

		.variable-label {
			font-size: 28rpx;
			margin-bottom: 16rpx;
		}

		.option-custom {
			height: 60rpx; /* 小屏设备的固定高度 */
		}

		.option-custom-active .custom-option {
			flex: 0 0 120rpx;
			min-width: 120rpx;
		}

		.inline-input {
			padding: 0 18rpx; /* 只设置水平内边距 */
			font-size: 26rpx;
			height: 52rpx; /* 小屏设备的固定高度 */
		}
	}

	/* 添加加载状态和交互细节 */
	.option-custom.loading {
		pointer-events: none;
		opacity: 0.7;
	}

	.option-custom.loading::after {
		content: "";
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 20rpx;
		height: 20rpx;
		border: 2rpx solid #7562FF;
		border-top: 2rpx solid transparent;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		z-index: 10;
	}

	@keyframes spin {
		0% { transform: translate(-50%, -50%) rotate(0deg); }
		100% { transform: translate(-50%, -50%) rotate(360deg); }
	}

	/* 增强焦点可见性 */
	.custom-option:focus-visible {
		outline: 2rpx solid #7562FF;
		outline-offset: 2rpx;
	}

	.inline-input:focus-visible {
		outline: 2rpx solid #7562FF;
		outline-offset: 2rpx;
	}

	/* 错误状态样式 */
	.option-custom.error {
		border-color: #FF6B6B;
		box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.15);
		animation: shake 0.5s ease-in-out;
	}

	@keyframes shake {
		0%, 100% { transform: translateX(0); }
		25% { transform: translateX(-4rpx); }
		75% { transform: translateX(4rpx); }
	}

	@media screen and (min-width: 1200rpx) {
		.select-options {
			gap: 20rpx;
		}

		.option-btn {
			min-width: 160rpx;
			padding: 24rpx 32rpx;
		}

		.variable-selector-box {
			padding: 28rpx;
			margin-bottom: 36rpx;
			min-height: 140rpx; /* 大屏设备的最小高度 */
		}

		.option-custom {
			height: 76rpx; /* 大屏设备的固定高度 */
		}

		.inline-input {
			padding: 0 24rpx; /* 只设置水平内边距 */
			height: 68rpx; /* 大屏设备的固定高度 */
		}
	}

</style>