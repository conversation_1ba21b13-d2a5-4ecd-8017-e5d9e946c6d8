# 激励广告功能测试计划

## 测试环境准备

### 1. 后端环境
- 确保后端服务正常运行
- 数据库连接正常
- 用户认证功能正常

### 2. 前端环境
- 微信开发者工具
- 真机测试环境
- 有效的用户登录状态

## 功能测试用例

### 测试用例 1: 广告初始化
**目标**: 验证广告实例正确初始化

**步骤**:
1. 打开支付页面
2. 检查控制台是否有广告相关日志
3. 确认广告按钮正常显示

**预期结果**:
- 无错误日志
- 广告按钮可见且可点击
- 显示正确的观看次数 (0/3)

### 测试用例 2: 获取观看次数
**目标**: 验证后端API正确返回观看次数

**步骤**:
1. 登录用户
2. 调用 `GET /wowpic/ad/watch-count` API
3. 检查返回数据

**预期结果**:
```json
{
  "count": 0,
  "maxCount": 3,
  "remaining": 3
}
```

### 测试用例 3: 首次观看广告
**目标**: 验证首次观看广告的完整流程

**步骤**:
1. 点击"观看激励广告"按钮
2. 等待广告加载
3. 完整观看广告
4. 检查奖励发放

**预期结果**:
- 广告正常播放
- 观看完成后显示"恭喜获得15哇图币！"
- 用户哇图币余额增加15
- 观看次数更新为 1/3

### 测试用例 4: 中途退出广告
**目标**: 验证用户中途退出广告的处理

**步骤**:
1. 点击"观看激励广告"按钮
2. 广告播放中途点击关闭
3. 检查是否给予奖励

**预期结果**:
- 显示"请完整观看广告才能获得奖励"
- 用户哇图币余额不变
- 观看次数不增加

### 测试用例 5: 达到每日上限
**目标**: 验证每日观看次数限制

**步骤**:
1. 连续观看3次广告
2. 尝试观看第4次
3. 检查按钮状态和提示

**预期结果**:
- 前3次正常获得奖励
- 第4次点击显示"今日观看次数已用完"
- 按钮变为禁用状态
- 标题显示"今日观看已达上限"

### 测试用例 6: 广告加载失败
**目标**: 验证广告加载失败的处理

**步骤**:
1. 在网络不佳环境下点击广告按钮
2. 等待加载超时
3. 检查错误处理

**预期结果**:
- 显示"广告加载失败，请稍后重试"
- 自动重试机制生效
- 用户可以再次尝试

### 测试用例 7: 后端奖励发放
**目标**: 验证后端奖励发放逻辑

**步骤**:
1. 直接调用 `POST /wowpic/ad/reward` API
2. 传入正确参数
3. 检查数据库变化

**预期结果**:
- API返回成功响应
- 用户哇图币余额增加
- `coin_transactions` 表新增记录

### 测试用例 8: 防刷机制
**目标**: 验证防重复领取机制

**步骤**:
1. 观看3次广告达到上限
2. 尝试直接调用奖励API
3. 检查是否被拒绝

**预期结果**:
- API返回"今日观看次数已达上限"
- 用户哇图币余额不变
- 不产生新的交易记录

## 边界测试

### 测试用例 9: 跨日期测试
**目标**: 验证每日限制的重置

**步骤**:
1. 在一天内观看3次广告
2. 等待到第二天
3. 尝试再次观看广告

**预期结果**:
- 第二天观看次数重置为 0/3
- 可以正常观看广告获得奖励

### 测试用例 10: 并发测试
**目标**: 验证并发观看的处理

**步骤**:
1. 多个用户同时观看广告
2. 检查服务器性能
3. 验证数据一致性

**预期结果**:
- 服务器正常响应
- 每个用户的数据正确
- 无数据竞争问题

## 性能测试

### 测试用例 11: 页面加载性能
**目标**: 验证广告功能对页面性能的影响

**步骤**:
1. 测量页面加载时间
2. 对比添加广告功能前后的性能
3. 检查内存使用情况

**预期结果**:
- 页面加载时间增加不超过500ms
- 内存使用合理
- 无内存泄漏

## 兼容性测试

### 测试用例 12: 不同设备测试
**目标**: 验证在不同设备上的兼容性

**测试设备**:
- iOS设备 (iPhone)
- Android设备
- 不同版本的微信

**预期结果**:
- 所有设备上功能正常
- UI显示正确
- 无兼容性问题

## 回归测试

### 测试用例 13: 原有功能验证
**目标**: 确保新功能不影响原有功能

**步骤**:
1. 测试充值功能
2. 测试新用户特惠
3. 测试页面导航

**预期结果**:
- 所有原有功能正常
- 无功能回退
- 用户体验良好

## 测试报告模板

```
测试日期: ____
测试人员: ____
测试环境: ____

| 测试用例 | 状态 | 备注 |
|---------|------|------|
| 广告初始化 | ✅/❌ |  |
| 获取观看次数 | ✅/❌ |  |
| 首次观看广告 | ✅/❌ |  |
| 中途退出广告 | ✅/❌ |  |
| 达到每日上限 | ✅/❌ |  |
| 广告加载失败 | ✅/❌ |  |
| 后端奖励发放 | ✅/❌ |  |
| 防刷机制 | ✅/❌ |  |

总体评价: ____
发现问题: ____
建议改进: ____
```
