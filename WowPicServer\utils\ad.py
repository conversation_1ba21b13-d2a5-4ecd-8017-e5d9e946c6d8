import logging
from datetime import datetime, date, timedelta
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import Optional
import pytz

from config import Config
from utils.auth import get_current_user, get_db
from database.models import User, CoinTransaction, CoinTransactionSource

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/wowpic/ad",
    tags=["广告"],
)

# 广告奖励配置
VIDEO_AD_REWARD = 15  # 激励视频广告奖励15哇图币
MAX_VIDEO_AD_PER_DAY = 3  # 每日最多观看3次激励视频广告

# ==================== Pydantic模型 ====================
class AdRewardRequest(BaseModel):
    adType: str  # 广告类型，如 'video'
    reward: int  # 期望奖励金额

class AdRewardResponse(BaseModel):
    success: bool
    message: str
    newBalance: Optional[int] = None
    todayWatchCount: Optional[int] = None

class AdWatchCountResponse(BaseModel):
    count: int
    maxCount: int
    remaining: int

# ==================== 业务逻辑 ====================
def _get_today_video_ad_count(db: Session, user_id: int) -> int:
    """获取用户今日观看激励视频广告的次数"""
    from sqlalchemy import text

    # 使用数据库的日期函数，数据库连接已设置为北京时区（+08:00）
    # DATE(created_at) = CURDATE() 会使用数据库设置的时区
    count = db.query(CoinTransaction).filter(
        CoinTransaction.user_id == user_id,
        CoinTransaction.source == CoinTransactionSource.VIDEO_AD,
        text("DATE(created_at) = CURDATE()")
    ).count()

    logger.info(f"用户 {user_id} 今日观看广告次数: {count}")
    return count

def _can_watch_video_ad(db: Session, user_id: int) -> bool:
    """检查用户今日是否还能观看激励视频广告"""
    today_count = _get_today_video_ad_count(db, user_id)
    return today_count < MAX_VIDEO_AD_PER_DAY

def _give_video_ad_reward(db: Session, user: User) -> bool:
    """给予用户激励视频广告奖励"""
    try:
        # 检查是否还能观看
        if not _can_watch_video_ad(db, user.id):
            return False
        
        # 增加哇图币
        user.coins += VIDEO_AD_REWARD
        
        # 记录流水
        db.add(
            CoinTransaction(
                user_id=user.id,
                change=VIDEO_AD_REWARD,
                balance=user.coins,
                source=CoinTransactionSource.VIDEO_AD,
                remark="观看激励视频广告奖励",
            )
        )
        
        db.commit()
        logger.info(f"用户 {user.id} 观看激励视频广告获得 {VIDEO_AD_REWARD} 哇图币")
        return True
        
    except Exception as e:
        db.rollback()
        logger.error(f"给予广告奖励失败: {e}")
        return False

# ==================== API路由 ====================
@router.get("/watch-count", response_model=AdWatchCountResponse)
async def get_ad_watch_count(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取用户今日广告观看次数"""
    try:
        today_count = _get_today_video_ad_count(db, current_user.id)
        remaining = max(0, MAX_VIDEO_AD_PER_DAY - today_count)
        
        return AdWatchCountResponse(
            count=today_count,
            maxCount=MAX_VIDEO_AD_PER_DAY,
            remaining=remaining
        )
    except Exception as e:
        logger.error(f"获取广告观看次数失败: {e}")
        raise HTTPException(status_code=500, detail="获取观看次数失败")

@router.post("/reward", response_model=AdRewardResponse)
async def give_ad_reward(
    request: AdRewardRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """给予用户广告观看奖励"""
    try:
        # 验证广告类型
        if request.adType != 'video':
            return AdRewardResponse(
                success=False,
                message="不支持的广告类型"
            )
        
        # 验证奖励金额
        if request.reward != VIDEO_AD_REWARD:
            return AdRewardResponse(
                success=False,
                message="奖励金额不正确"
            )
        
        # 检查今日观看次数
        if not _can_watch_video_ad(db, current_user.id):
            return AdRewardResponse(
                success=False,
                message="今日观看次数已达上限"
            )
        
        # 给予奖励
        success = _give_video_ad_reward(db, current_user)
        if success:
            today_count = _get_today_video_ad_count(db, current_user.id)
            return AdRewardResponse(
                success=True,
                message="奖励发放成功",
                newBalance=current_user.coins,
                todayWatchCount=today_count
            )
        else:
            return AdRewardResponse(
                success=False,
                message="奖励发放失败"
            )
            
    except Exception as e:
        logger.error(f"处理广告奖励失败: {e}")
        raise HTTPException(status_code=500, detail="处理广告奖励失败")
