<!DOCTYPE html>
<html>
<head>
    <title>系统信息测试</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>系统信息检测测试</h1>
    <div id="result"></div>
    
    <script>
        // 模拟微信小程序环境 - iOS设备
        const wx = {
            getDeviceInfo: function(options) {
                console.log('调用 getDeviceInfo')
                setTimeout(() => {
                    options.success({
                        platform: 'ios', // 可以改为 'android' 测试
                        system: 'iOS 15.0',
                        model: 'iPhone 13',
                        brand: 'Apple'
                    })
                }, 100)
            },
            getSystemInfoAsync: function(options) {
                console.log('调用 getSystemInfoAsync')
                setTimeout(() => {
                    options.success({
                        platform: 'ios',
                        system: 'iOS 15.0',
                        model: 'iPhone 13',
                        brand: 'Apple',
                        version: '8.0.5',
                        language: 'zh_CN'
                    })
                }, 100)
            },
            getSystemInfo: function(options) {
                console.log('调用 getSystemInfo')
                setTimeout(() => {
                    options.success({
                        platform: 'ios',
                        system: 'iOS 15.0',
                        model: 'iPhone 13',
                        brand: 'Apple',
                        version: '8.0.5',
                        language: 'zh_CN'
                    })
                }, 100)
            }
        }
        
        // 模拟uni-app
        const uni = {
            getSystemInfoSync: function() {
                console.log('调用 uni.getSystemInfoSync')
                return {
                    platform: 'ios',
                    system: 'iOS 15.0',
                    model: 'iPhone 13',
                    brand: 'Apple',
                    version: '8.0.5',
                    language: 'zh_CN'
                }
            }
        }

        // 测试系统信息检测逻辑
        let systemInfo = null
        let isIOS = false

        // 检测是否为iOS系统
        function detectIOS(systemInfo) {
            if (!systemInfo) return false

            // 方法1: 检查platform字段
            const platformIOS = systemInfo.platform === 'ios'

            // 方法2: 检查system字段是否包含iOS
            const systemIOS = systemInfo.system && systemInfo.system.toLowerCase().includes('ios')

            // 方法3: 检查model字段是否包含iPhone或iPad
            const modelIOS = systemInfo.model && (
                systemInfo.model.toLowerCase().includes('iphone') ||
                systemInfo.model.toLowerCase().includes('ipad')
            )

            const result = platformIOS || systemIOS || modelIOS

            console.log('iOS检测详情:')
            console.log('- platform字段:', systemInfo.platform, '-> iOS:', platformIOS)
            console.log('- system字段:', systemInfo.system, '-> iOS:', systemIOS)
            console.log('- model字段:', systemInfo.model, '-> iOS:', modelIOS)
            console.log('- 最终结果:', result)

            return result
        }
        
        function getSystemInfo() {
            console.log('开始获取系统信息...')
            
            // 优先使用新的API
            if (typeof wx !== 'undefined' && wx.getDeviceInfo) {
                try {
                    wx.getDeviceInfo({
                        success: (res) => {
                            console.log('getDeviceInfo 成功:', res)
                            systemInfo = res
                            isIOS = detectIOS(res)
                            console.log('检测到平台:', res.platform, '系统:', res.system, '是否为iOS:', isIOS)
                            updateResult()
                        },
                        fail: (err) => {
                            console.error('getDeviceInfo 失败:', err)
                            fallbackGetSystemInfo()
                        }
                    })
                } catch (error) {
                    console.error('getDeviceInfo 异常:', error)
                    fallbackGetSystemInfo()
                }
            } else {
                console.log('getDeviceInfo 不可用，使用备用方案')
                fallbackGetSystemInfo()
            }
        }
        
        function fallbackGetSystemInfo() {
            console.log('使用备用方案获取系统信息...')
            
            if (typeof wx !== 'undefined' && wx.getSystemInfoAsync) {
                wx.getSystemInfoAsync({
                    success: (res) => {
                        console.log('getSystemInfoAsync 成功:', res)
                        systemInfo = res
                        isIOS = detectIOS(res)
                        console.log('检测到平台:', res.platform, '系统:', res.system, '是否为iOS:', isIOS)
                        updateResult()
                    },
                    fail: (err) => {
                        console.error('getSystemInfoAsync 失败:', err)
                        finalFallbackGetSystemInfo()
                    }
                })
            } else if (typeof wx !== 'undefined' && wx.getSystemInfo) {
                wx.getSystemInfo({
                    success: (res) => {
                        console.log('getSystemInfo 成功:', res)
                        systemInfo = res
                        isIOS = detectIOS(res)
                        console.log('检测到平台:', res.platform, '系统:', res.system, '是否为iOS:', isIOS)
                        updateResult()
                    },
                    fail: (err) => {
                        console.error('getSystemInfo 失败:', err)
                        finalFallbackGetSystemInfo()
                    }
                })
            } else {
                console.warn('所有系统信息API都不可用')
                finalFallbackGetSystemInfo()
            }
        }
        
        function finalFallbackGetSystemInfo() {
            console.log('使用最终备用方案...')
            try {
                const systemInfoSync = uni.getSystemInfoSync()
                console.log('uni.getSystemInfoSync 成功:', systemInfoSync)
                systemInfo = systemInfoSync
                isIOS = detectIOS(systemInfoSync)
                console.log('检测到平台:', systemInfoSync.platform, '系统:', systemInfoSync.system, '是否为iOS:', isIOS)
                updateResult()
            } catch (error) {
                console.error('uni.getSystemInfoSync 失败:', error)
                isIOS = false
                console.log('无法获取系统信息，默认设置为非iOS')
                updateResult()
            }
        }
        
        function updateResult() {
            const resultDiv = document.getElementById('result')
            resultDiv.innerHTML = `
                <h2>检测结果:</h2>
                <p><strong>是否为iOS:</strong> ${isIOS ? '是' : '否'}</p>
                <p><strong>平台:</strong> ${systemInfo?.platform || '未知'}</p>
                <p><strong>系统:</strong> ${systemInfo?.system || '未知'}</p>
                <p><strong>设备型号:</strong> ${systemInfo?.model || '未知'}</p>
                <p><strong>品牌:</strong> ${systemInfo?.brand || '未知'}</p>
                <hr>
                <h3>完整系统信息:</h3>
                <pre>${JSON.stringify(systemInfo, null, 2)}</pre>
            `
        }
        
        // 启动测试
        getSystemInfo()
    </script>
</body>
</html>
