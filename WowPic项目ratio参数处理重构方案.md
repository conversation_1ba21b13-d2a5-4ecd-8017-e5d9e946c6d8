# WowPic项目ratio参数处理重构方案

## 1. 重构背景与目标

### 1.1 当前问题分析

通过对WowPic项目的深入分析，发现ratio参数处理流程存在以下关键问题：

1. **数据不一致风险**：API层构建提示词后存储，生成服务又从提示词中提取ratio参数
2. **重复处理逻辑**：多个层级都在处理ratio参数，导致代码冗余和维护困难
3. **前端传递不统一**：generate.vue和create.vue采用不同的ratio传递方式
4. **循环依赖问题**：存储的prompt包含ratio，但生成时又要提取ratio单独传递

### 1.2 重构目标

**核心原则**：存储到数据表中的提示词必须是提交给AI生成前的最终提示词，确保数据库中的prompt字段与实际发送给AI服务的内容完全一致。

**具体目标**：
- 统一数据流转规则，消除重复处理
- 简化代码逻辑，提高可维护性
- 确保数据一致性，避免参数丢失或错误
- 建立清晰的职责边界

## 2. 重构方案设计

### 2.1 统一数据流转规则

```
前端选择ratio → API接口接收 → 构建最终AI提示词 → 存储到数据库 → 生成服务直接使用 → AI厂商直接处理
```

**关键原则**：
- 数据库中存储的prompt = 发送给AI的prompt（完全一致）
- 生成服务不再进行任何提示词修改或参数提取
- 所有参数处理（包括ratio、n等）在API层完成
- AI厂商接收完整提示词，不再重复处理ratio

### 2.2 各层职责重新定义

| 层级 | 职责 | ratio处理方式 |
|------|------|---------------|
| 前端UI层 | 提供ratio选择界面，统一传递参数 | 选择ratio值，通过请求体传递 |
| API路由层 | 接收参数，构建最终AI提示词，存储 | 将ratio嵌入到最终提示词中 |
| 生成服务层 | 获取存储的提示词，直接调用AI | 不处理ratio，直接使用完整提示词 |
| AI厂商层 | 接收完整提示词，调用AI服务 | 不检查不添加，直接使用提示词 |

### 2.3 ratio参数格式标准

**统一格式**：在提示词末尾添加ratio信息
```
{基础提示词内容}
"ratio": "{ratio_value}"
```

**示例**：
```
一只可爱的橘猫在阳光下睡觉
"ratio": "3:4"
```

## 3. 具体实施方案

### 3.1 前端层修改（高优先级）

#### 3.1.1 统一generate.vue的ratio传递方式

**当前问题**：generate.vue将ratio嵌入到detail变量中，导致传递方式不一致

**修改方案**：
```javascript
// 移除在变量中嵌入ratio的逻辑
if (variable.name === "detail") {
    vars[variable.name] = detailContent; // 不再添加ratio
}

// 统一在请求体中传递ratio
const reqBody = {
    variables: vars,
    ratio: this.aspectRatios[this.selectedRatioIndex].value,
    source_image_urls: uploadedUrls
};
```

#### 3.1.2 保持create.vue现有方式

create.vue已经采用正确的传递方式，无需修改：
```javascript
const body = { 
    prompt: this.prompt, 
    ratio: selectedRatio,
    source_image_urls: uploadedUrls 
};
```

### 3.2 API路由层修改（高优先级）

#### 3.2.1 统一提示词构建函数

创建统一的提示词构建函数：
```python
def build_final_ai_prompt(style, prompt, variables, ratio):
    """构建最终发送给AI的提示词"""
    # 1. 基础提示词处理
    if style.template_type == TemplateType.VARIABLE_PROMPT:
        base_prompt = process_variable_prompt(style, variables)
    else:
        base_prompt = prompt if prompt is not None else style.prompt_template

    # 2. 添加ratio参数（如果不是默认值）
    if ratio and ratio != "1:1":
        base_prompt = f"{base_prompt}\n\"ratio\": \"{ratio}\""

    # 3. 不再添加n参数，让AI模型使用默认生成数量
    return base_prompt
```

#### 3.2.2 修改所有生成接口

在所有生成接口中使用统一的构建函数：
```python
# 构建最终AI提示词
final_prompt = build_final_ai_prompt(
    style=style,
    prompt=prompt,
    variables=variables,
    ratio=ratio
)

# 存储包含所有参数的最终提示词
generation = Generation(
    task_id=str(uuid.uuid4()),
    user_id=current_user.id,
    style_id=style.id,
    prompt=final_prompt,  # 存储最终的完整提示词
    images_count=1,  # 固定为1，实际生成数量由AI模型决定
    status=GenerationStatus.PENDING,
    cost=style.cost,
    created_at=datetime.now()
)
```

### 3.3 生成服务层简化（中优先级）

#### 3.3.1 移除ratio提取逻辑

**当前问题代码**：
```python
# 需要删除的代码
ratio_match = re.search(r'"ratio":\s*"([^"]+)"', prompt_text)
if ratio_match:
    ratio = ratio_match.group(1)
```

**修改后的简化逻辑**：
```python
async def process_generation(generation_id: int):
    generation = get_generation_by_id(generation_id)
    
    # 直接使用存储的完整提示词，不进行任何修改
    params = {
        "prompt": generation.prompt,  # 直接使用数据库中的最终提示词
        "source_image_urls": [img.image_url for img in generation.source_images],
        "ratio": "1:1"  # 固定默认值，因为ratio已包含在prompt中
    }
    
    # 调用AI生成
    handler = StyleFactory.get_style_implementation(generation.style.model_identifier)
    result_urls = await handler.generate(params)
```

### 3.4 AI厂商层简化（中优先级）

#### 3.4.1 移除重复ratio处理

**修改所有厂商的generate_image方法**：

**AIHubMix厂商修改**：
```python
async def generate_image(self, prompt: str, source_images: List[str], ratio: str = "1:1") -> List[str]:
    """调用 AIHubMix GPT-4o-image 接口生成图片"""
    try:
        # 直接使用传入的prompt，不再检查和添加ratio参数
        # 因为prompt已经在API层包含了所有必要的参数
        return await self._generate_with_chat(prompt, source_images)
    except Exception as e:
        # 错误处理逻辑保持不变
        logger.error(f"AihubmixProvider API调用失败: {e}")
        raise RuntimeError("图片生成失败，请稍后再试")
```

**兔子厂商修改**：
```python
async def generate_image(self, prompt: str, source_images: List[str], ratio: str = "1:1") -> List[str]:
    """调用兔子接口生成图片"""
    try:
        # 直接使用传入的prompt，不再添加ratio参数
        enhanced_prompt = prompt  # 不再检查和修改
        
        # 其余逻辑保持不变
        # ...
    except Exception as e:
        logger.error(f"TuziProvider API调用失败: {e}")
        raise RuntimeError("图片生成失败，请稍后再试")
```

## 4. 数据一致性验证

### 4.1 存储验证规则

- 数据库中的`generation.prompt`字段必须包含所有AI生成所需的参数
- 该字段的内容应该可以直接发送给AI服务而无需任何修改
- ratio参数必须以`"ratio": "{value}"`格式包含在prompt中

### 4.2 测试验证方案

```python
def test_ratio_parameter_consistency():
    """测试ratio参数一致性"""
    # 1. 测试风格生成接口
    response = client.post("/wowpic/generate/1/async", {
        "variables": {"detail": "一只可爱的猫"},
        "ratio": "3:4"
    })
    
    # 2. 验证存储的generation记录
    generation = db.query(Generation).filter_by(task_id=response.task_id).first()
    
    # 3. 验证存储的prompt包含ratio参数
    assert '"ratio": "3:4"' in generation.prompt
    assert "一只可爱的猫" in generation.prompt
    
    # 4. 测试自由创作接口
    response2 = client.post("/wowpic/generate/generic/async", {
        "prompt": "一只可爱的狗",
        "ratio": "4:3"
    })
    
    generation2 = db.query(Generation).filter_by(task_id=response2.task_id).first()
    assert '"ratio": "4:3"' in generation2.prompt
```

## 5. 实施优先级与时间安排

### 5.1 第一阶段（高优先级）
- [ ] 修改generate.vue的ratio传递方式
- [ ] 创建统一的提示词构建函数
- [ ] 修改所有API接口使用新的构建函数

### 5.2 第二阶段（中优先级）
- [ ] 简化generation_service.py中的处理逻辑
- [ ] 修改所有AI厂商的generate_image方法
- [ ] 添加数据一致性测试

### 5.3 第三阶段（低优先级）
- [ ] 代码重构和优化
- [ ] 文档更新
- [ ] 性能测试和监控

## 6. 风险评估与回滚方案

### 6.1 潜在风险
- 修改过程中可能影响现有功能
- 数据格式变更可能导致兼容性问题
- AI厂商接口变更可能影响生成效果

### 6.2 回滚方案
- 保留原有代码的备份分支
- 分阶段实施，每个阶段都可以独立回滚
- 添加功能开关，可以快速切换新旧逻辑

### 6.3 兼容性处理
- 对于已存储的generation记录，保持现有处理逻辑
- 新的生成请求使用新规则
- 添加版本标识来区分新旧数据

## 7. 预期效果

### 7.1 代码质量提升
- 消除重复代码，提高可维护性
- 统一处理逻辑，降低bug风险
- 清晰的职责分工，便于团队协作

### 7.2 数据一致性保障
- 确保存储的prompt与AI接收的内容完全一致
- 消除参数丢失或错误的风险
- 便于问题追踪和调试

### 7.3 系统性能优化
- 减少不必要的字符串处理操作
- 简化调用链路，提高响应速度
- 降低系统复杂度，提高稳定性

这个重构方案将彻底解决当前ratio参数处理中的问题，建立清晰、一致、可维护的参数传递机制。
