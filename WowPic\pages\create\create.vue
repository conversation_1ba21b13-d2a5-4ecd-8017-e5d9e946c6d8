<template>
	<view class="create-container">
		<!-- 可滚动内容区域 -->
		<scroll-view class="scroll-container" scroll-y="true" show-scrollbar="false" enhanced="true">
			<!-- 顶部安全区域 -->
			<view class="safe-area"></view>
			
			<!-- 创作模式选择工具栏 -->
			<view class="creation-mode-options">
				<view 
					class="mode-btn" 
					:class="{'active': selectedModeIndex === 0}"
					@click="selectMode(0)"
				>
					<view class="mode-icon free"></view>
					<text class="mode-btn-text">自由创作</text>
				</view>
				<view 
					class="mode-btn" 
					:class="{'active': selectedModeIndex === 1}"
					@click="selectMode(1)"
				>
					<view class="mode-icon style"></view>
					<text class="mode-btn-text">风格创作</text>
				</view>
			</view>
			
			<!-- 上传图片区域 -->
			<!-- <view class="section upload-section">
				<view class="section-title-bar">
					<text class="section-title">上传图片</text>
					<text class="section-subtitle">上传参考图片效果更佳</text>
				</view>
				
				<view class="multi-upload-container">
					<scroll-view v-if="tempImages.length > 0" class="uploaded-images-scroll" scroll-x="true" show-scrollbar="false" enhanced="true">
						<view class="uploaded-images-list">
							<view class="image-item" v-for="(img, index) in tempImages" :key="index">
								<image class="preview-image" :src="img" mode="aspectFill"></image>
								<view class="image-overlay">
									<view class="delete-icon" @click.stop="deleteImage(index)"></view>
								</view>
								<view class="image-index">{{index + 1}}</view>
							</view>
							
							<view class="image-item add-more" @click="chooseImage" v-if="tempImages.length < maxImagesCount">
								<view class="upload-icon"></view>
								<text class="add-text">添加图片</text>
							</view>
						</view>
					</scroll-view>
					
					<view v-if="tempImages.length === 0" class="upload-box empty" @click="chooseImage">
						<view class="upload-icon large"></view>
					</view>
				</view>
			</view> -->
			
			<!-- 提示词输入区域 -->
			<view class="section prompt-section">
				<view class="section-title-bar">
					<text class="section-title">创作内容</text>
					<text class="section-subtitle">详细的提示词能带来更好的效果</text>
				</view>
				
				<!-- 集成上传图片区域 -->
				<view class="multi-upload-container integrated">
					<!-- 已上传的图片列表 -->
					<scroll-view v-if="tempImages.length > 0" class="uploaded-images-scroll" scroll-x="true" show-scrollbar="false" enhanced="true">
						<view class="uploaded-images-list">
							<view class="image-item" v-for="(img, index) in tempImages" :key="index">
								<image class="preview-image" :src="img" mode="aspectFill"></image>
								<view class="image-overlay">
									<view class="delete-icon" @click.stop="deleteImage(index)"></view>
								</view>
								<view class="image-index">{{index + 1}}</view>
							</view>
							
							<!-- 添加更多图片的按钮 -->
							<view class="image-item add-more" @click="chooseImage" v-if="tempImages.length < maxImagesCount">
								<view class="upload-icon"></view>
								<text class="add-text">添加</text>
							</view>
						</view>
					</scroll-view>
					
					<!-- 空状态 - 当没有图片时显示 -->
					<view v-if="tempImages.length === 0" class="upload-box-compact" @click="chooseImage">
						<view class="upload-icon-compact"></view>
						<text class="upload-text-compact">添加参考图 (可选)</text>
					</view>
				</view>
				
				<view class="prompt-input-box">
					<textarea class="prompt-input" placeholder="请描述你想要的图片效果..." v-model="prompt" maxlength="1000" />
					<view class="prompt-length">{{prompt.length}}/3000</view>
				</view>

				<!-- 注释掉提示词标签
				<view class="prompt-tags">
					<view class="tag" @click="addTag('动漫风格')">动漫风格</view>
					<view class="tag" @click="addTag('写实风格')">写实风格</view>
					<view class="tag" @click="addTag('油画效果')">油画效果</view>
				</view>
				-->
			</view>
			
			<!-- 尺寸选择区域 -->
			<view class="aspect-ratio-options">
				<view 
					class="aspect-btn" 
					:class="{'active': selectedRatioIndex === 0}"
					@click="selectRatio(0)"
				>
					<view class="aspect-icon square"></view>
					<text class="aspect-btn-text">正方形</text>
				</view>
				<view 
					class="aspect-btn" 
					:class="{'active': selectedRatioIndex === 1}"
					@click="selectRatio(1)"
				>
					<view class="aspect-icon portrait"></view>
					<text class="aspect-btn-text">竖屏</text>
				</view>
				<view 
					class="aspect-btn" 
					:class="{'active': selectedRatioIndex === 2}"
					@click="selectRatio(2)"
				>
					<view class="aspect-icon landscape"></view>
					<text class="aspect-btn-text">横屏</text>
				</view>
			</view>
			
			<!-- 底部操作区 -->
			<view class="bottom-action">
				<view class="coin-info">
					<image class="coin-icon" src="/static/coins.png"></image>
					<view class="coin-text">{{cost}} 哇图币/次</view>
				</view>
				<view class="generate-btn" @click="generateImage" :class="{'disabled': isGenerating || !prompt.trim()}">
					<text>{{isGenerating ? '生成中...' : '开始生成'}}</text>
					<view class="btn-icon" v-if="!isGenerating"></view>
				</view>
			</view>
			
			<!-- 结果区域 -->
			<view class="section result-section">
				<view class="section-title-bar">
					<text class="section-title">生成结果</text>
					<text class="section-subtitle">{{getResultSubtitle()}}</text>
				</view>

				<!-- 生成中状态 -->
				<view v-if="showGeneratingStatus" class="generating-status"></view>

				<!-- 生成失败状态 -->
				<view v-else-if="generationError" class="error-status">
					<view class="error-icon"></view>
					<view class="error-text">{{generationError}}</view>
				</view>

				<!-- 未生成状态 -->
				<view v-else-if="showWaitingStatus" class="waiting-status">
					<view class="waiting-icon"></view>
					<view class="waiting-text">请点击"开始生成"，AI将为您带来惊喜...</view>
				</view>

				<!-- 生成结果 -->
				<view v-else-if="showResultBox" class="result-box">
					<!-- 使用垂直平铺布局显示多张图片 -->
					<view class="result-images-list">
						<view v-for="(imageUrl, idx) in generatedImages" :key="idx" class="result-image-item">
							<image
								class="result-image"
								:src="getImageUrl(imageUrl)"
								mode="widthFix"
								@tap="previewImage(imageUrl)"
							></image>
						</view>
					</view>

					<!-- 操作按钮 -->
					<view class="action-row">
						<view class="action-btn save-btn" @click="saveImage">
							<view class="action-icon save-icon"></view>
							<text>保存图片</text>
						</view>
						<button class="action-btn share-btn" open-type="share" @click="prepareShare">
							<view class="action-icon share-icon"></view>
							<text>分享作品</text>
						</button>
					</view>
				</view>
			</view>
		</scroll-view>
		
		<!-- 风格选择器组件 -->
		<StyleSelector
			:show="showStyleSelector"
			:currentStyleId="null"
			@select="onStyleSelect"
			@cancel="onStyleCancel"
		/>
	</view>
</template>

<script>
	import request from '../../utils/request.js'
	import StyleSelector from '../../components/StyleSelector.vue'
	import imageMixin from '../../utils/imageMixin.js'
	import { getImageUrl } from '../../utils/config.js'

	export default {
		components: {
			StyleSelector
		},
		mixins: [imageMixin],

		computed: {
			// 是否显示生成中状态
			showGeneratingStatus() {
				const hasImages = this.generatedImages && this.generatedImages.length > 0;
				return this.generationInProgress && !hasImages && !this.generationError;
			},

			// 是否显示等待状态
			showWaitingStatus() {
				const hasImages = this.generatedImages && this.generatedImages.length > 0;
				return !hasImages && !this.generationError && !this.generationInProgress;
			},

			// 是否显示结果区域
			showResultBox() {
				return this.generatedImages && this.generatedImages.length > 0;
			}
		},
		data() {
			return {
				prompt: '', // 用户输入的提示词
				selectedRatioIndex: 1, // 默认选择竖屏比例(3:4)
				aspectRatios: [
					{ label: '正方形', value: '1:1' },
					{ label: '竖屏', value: '3:4' },
					{ label: '横屏', value: '4:3' }
				],
				isGenerating: false, // 是否正在生成图片
				generatedImages: [], // 生成的图片URL数组
				cost: 50, // 生成花费的哇图币
				userCoins: 0, // 用户当前哇图币余额
				currentShareImageUrl: '', // 用于存储当前预览的图片URL，用于分享
				selectedModeIndex: 0, // 默认自由创作模式
				previousModeIndex: 0, // 记录之前的模式，用于取消时恢复
				showStyleSelector: false, // 控制风格选择器的显示
				// 新增：生成状态管理
				generationInProgress: false,  // 表示是否有正在进行的生成任务
				generationTaskId: '',         // 当前生成任务ID
				generationStatus: '',         // 当前生成状态 (PENDING, SUCCESS, FAILED)
				generationCheckTimer: null,   // 状态检查定时器
				generationError: '',          // 生成失败时的错误信息
				currentGenerationId: null,    // 当前生成记录ID
				// 订阅消息模板ID（运行时从后端获取）
				subscriptionTemplateId: '',
			}
		},
		onLoad() {
			// 先获取订阅消息模板ID
			this.fetchSubscribeTemplateId();
			// 加载用户信息
			this.loadUserInfo();
		},

		onShow() {
			// 每次显示页面时更新用户信息（可能从其他页面修改了哇图币）
			this.loadUserInfo();

			// 启用分享菜单
			uni.showShareMenu({
				withShareTicket: true,
				menus: ['shareAppMessage']
			});
		},

		onHide() {
			// 页面隐藏时暂停定时器，避免后台运行
			if (this.generationCheckTimer) {
				clearInterval(this.generationCheckTimer);
				this.generationCheckTimer = null;
			}
		},

		onUnload() {
			// 清除定时器
			if (this.generationCheckTimer) {
				clearInterval(this.generationCheckTimer);
				this.generationCheckTimer = null;
			}
		},
		onShow() {
			// 每次页面显示时重置为自由创作模式
			this.selectedModeIndex = 0;
			this.previousModeIndex = 0;
			// 确保风格选择器隐藏
			this.showStyleSelector = false;
			// 每次显示页面时更新用户信息（可能从其他页面修改了哇图币）
			this.loadUserInfo();
		},
		methods: {
			// 将导入的getImageUrl添加到方法中，使其在模板中可用
			getImageUrl,

			// 添加标签
			addTag(tag) {
				if (this.prompt.length > 0 && !this.prompt.endsWith('、') && !this.prompt.endsWith('，') && !this.prompt.endsWith(' ')) {
					this.prompt += '、';
				}
				this.prompt += tag;
			},
			
			// 选择图片比例
			selectRatio(index) {
				this.selectedRatioIndex = index;
			},

			// 选择创作模式
			selectMode(index) {
				if (index === 1) {
					// 记录当前模式，以便取消时恢复
					this.previousModeIndex = this.selectedModeIndex;
					// 临时设置为风格创作模式（用于UI显示）
					this.selectedModeIndex = index;
					// 显示风格选择器
					this.showStyleSelector = true;
				} else {
					// 直接切换到自由创作模式
					this.previousModeIndex = this.selectedModeIndex;
					this.selectedModeIndex = index;
				}
			},
			
			// 处理风格选择
			onStyleSelect(style) {
				// 关闭风格选择器
				this.showStyleSelector = false;

				// 添加日志输出，便于调试
				console.log('风格选择：', style);
				console.log('准备跳转到生成页面，styleId =', style.id);

				// 跳转到生成页面，并传递风格ID
				uni.navigateTo({
					url: `/pages/generate/generate?styleId=${style.id}`,
					success: () => {
						console.log('跳转成功');
					},
					fail: (err) => {
						console.error('跳转失败', err);
					}
				});
			},

			// 处理取消风格选择
			onStyleCancel() {
				// 关闭风格选择器
				this.showStyleSelector = false;
				// 恢复到之前的模式
				this.selectedModeIndex = this.previousModeIndex;
			},

			// 滚动到结果区域
			scrollToResult() {
				// 使用nextTick确保DOM已更新
				this.$nextTick(() => {
					uni.pageScrollTo({
						selector: '.result-section',
						duration: 300
					});
				});
			},

			// 开始定时检查生成状态
			startStatusCheck() {
				// 清除可能存在的旧定时器
				if (this.generationCheckTimer) {
					clearInterval(this.generationCheckTimer);
				}

				// 每3秒检查一次状态
				this.generationCheckTimer = setInterval(() => {
					this.checkGenerationStatus();
				}, 3000);
			},

			// 检查生成状态
			async checkGenerationStatus() {
				if (!this.generationTaskId) return;

				try {
					const result = await request.get(`/wowpic/generate/status/${this.generationTaskId}`, {}, {
						hideErrorTips: true
					});

					console.log('生成状态检查结果:', result);

					if (result.status === 'SUCCESS' || result.status === 'PARTIAL') {
						// 生成成功
						this.isGenerating = false;
						this.generationInProgress = false;
						clearInterval(this.generationCheckTimer);
						this.generationCheckTimer = null;

						// 处理生成结果
						this.handleGenerationSuccess(result);

						// 生成成功后，发送订阅消息
						try {
							// 创建模板数据，符合模板格式要求
							const now = new Date();
							const currentTime = now.toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'});

							// 根据新的订阅消息模板字段组装数据
							const templateData = {
								thing1: {
									value: `自由创作已完成，点击查看>>`
								},
								phrase6: {
									value: '生成成功'
								},
								time5: {
									value: currentTime // 只传入时间，避免47003错误
								},
								thing2: {
									value: '打开小程序查看更多风格~'
								}
							};

							console.log('准备发送订阅消息通知，模板数据:', templateData);
							// 调用后端接口发送订阅消息（忽略失败，不阻塞主流程）
							try {
								await request.post('/wowpic/notify/send', {
									templateId: this.subscriptionTemplateId,
									data: templateData,
									page: '/pages/index/index',
									generationId: this.currentGenerationId
								}, { hideErrorTips: true });
							} catch (e) {
								console.error('调用订阅消息接口失败:', e);
							}
						} catch (notifyErr) {
							console.error('发送订阅消息通知失败:', notifyErr);
							// 通知失败不影响主流程
						}

						// 显示成功提示
						uni.showToast({
							title: result.status === 'PARTIAL' ?
								`已成功生成${(result.generated_image_urls || []).length}张图片，部分图片生成失败` :
								'生成完成',
							icon: result.status === 'PARTIAL' ? 'none' : 'success',
							duration: 2000
						});

						// 滚动到结果区域
						this.scrollToResult();
					} else if (result.status === 'FAILED') {
						// 生成失败
						this.isGenerating = false;
						this.generationInProgress = false;
						clearInterval(this.generationCheckTimer);
						this.generationCheckTimer = null;

						// 设置错误信息
						this.generationError = result.error_message || '生成失败，请联系客服';

						// 显示失败提示
						uni.showToast({
							title: this.generationError,
							icon: 'none',
							duration: 2000
						});

						// 滚动到结果区域
						this.scrollToResult();
					}
					// PENDING或PROCESSING状态继续等待
					else if (result.status === 'PENDING' || result.status === 'PROCESSING') {
						// 确保生成中状态正确设置
						this.generationInProgress = true;
						this.isGenerating = true;
					}

				} catch (e) {
					console.error('检查生成状态失败:', e);
					// 出错不中断轮询
				}
			},

			// 处理生成成功的结果
			handleGenerationSuccess(result) {
				// 保存生成记录ID，用于订阅消息
				if (result.id) {
					this.currentGenerationId = result.id;
				}

				if (result.generated_image_urls && result.generated_image_urls.length > 0) {
					// 使用Vue.set确保响应式更新
					this.$set(this, 'generatedImages', result.generated_image_urls);
				} else if (result.generated_image_url) {
					// 兼容单图返回格式
					this.$set(this, 'generatedImages', [result.generated_image_url]);
				}
			},

			// 生成图片
			async generateImage() {
				// 提示词是必需的
				if (!this.prompt.trim()) {
					uni.showToast({
						title: '请输入提示词',
						icon: 'none'
					});
					return;
				}

				if (this.isGenerating || this.generationInProgress) {
					uni.showToast({
						title: '正在生成中，请稍候',
						icon: 'none'
					});
					return;
				}

				// 检查哇图币余额是否足够
				if (this.userCoins < this.cost) {
					uni.showModal({
						title: '哇图币不足',
						content: `生成需要${this.cost}哇图币，您当前余额为${this.userCoins}哇图币`,
						confirmText: '去获取',
						cancelText: '取消',
						success: (res) => {
							if (res.confirm) {
								// 跳转到支付页面获取哇图币
								uni.navigateTo({
									url: '/pages/pay/pay'
								});
							}
						}
					});
					return;
				}

				// 确保已获取订阅消息模板ID
				if (!this.subscriptionTemplateId) {
					try {
						await this.fetchSubscribeTemplateId();
					} catch (e) {
						console.error('获取订阅消息模板ID失败:', e);
					}
				}

				// 先请求订阅消息权限（仅当有模板ID时）
				try {
					// 使用小程序的订阅消息API
					if (this.subscriptionTemplateId) {
						await new Promise((resolve) => {
							uni.requestSubscribeMessage({
								tmplIds: [this.subscriptionTemplateId],
								success: (res) => {
									console.log('订阅消息请求结果:', res);
									// 记录用户是否同意接收通知，但无论如何都继续生成流程
									if (res[this.subscriptionTemplateId] === 'accept') {
										console.log('用户同意接收通知');
									} else {
										console.log('用户拒绝接收通知或请求失败');
									}
									resolve();
								},
								fail: (err) => {
									console.error('订阅消息请求失败:', err);
									// 失败也继续生成流程
									resolve();
								}
							});
						});
					}
				} catch (subscribeErr) {
					console.error('订阅消息异常:', subscribeErr);
					// 出现异常也继续生成流程
				}

				// 清除之前的生成结果，确保显示生成中状态
				this.generatedImages = [];
				this.generationError = '';

				// 设置生成状态
				this.isGenerating = true;
				this.generationInProgress = true;

				uni.showLoading({
					title: '正在上传图片...'
				});

				try {
					// 上传所有图片到后端，获取URL数组
					const uploadedUrls = await this.uploadImage();

					uni.showLoading({
						title: '正在提交任务...'
					});

					const selectedRatio = this.aspectRatios[this.selectedRatioIndex].value;

					// 调用异步生成接口避免超时
					const body = { prompt: this.prompt, ratio: selectedRatio };
					if (uploadedUrls.length > 0) body.source_image_urls = uploadedUrls;
					const resp = await request.post('/wowpic/generate/generic/async', body);

					// 异步生成成功，开始状态检查
					if (resp && resp.task_id) {
						// 保存任务ID
						this.generationTaskId = resp.task_id;
						this.generationStatus = 'PENDING';

						// 刷新用户哇图币余额
						this.loadUserInfo();

						// 隐藏加载提示
						uni.hideLoading();

						// 显示生成中提示
						uni.showToast({
							title: '已开始生成，稍后将展示结果',
							icon: 'none',
							duration: 2000
						});

						// 滚动到结果区域
						this.scrollToResult();

						// 开始定期检查状态
						this.startStatusCheck();
					} else {
						throw new Error('提交生成任务失败');
					}

				} catch (error) {
					console.error('生成图片失败:', error);

					// 重置状态
					this.isGenerating = false;
					this.generationInProgress = false;
					uni.hideLoading();

					// 显示错误提示
					uni.showToast({
						title: '生成失败，请联系客服',
						icon: 'none'
					});
				}
			},

			// 获取订阅消息模板ID
			async fetchSubscribeTemplateId() {
				try {
					const res = await request.get('/wowpic/notify/template-id', {}, { hideErrorTips: true });
					if (res && res.templateId) {
						this.subscriptionTemplateId = res.templateId;
					}
				} catch (e) {
					console.error('获取订阅模板ID失败', e);
				}
			},

			// 获取结果区域的副标题
			getResultSubtitle() {
				// 与 generate.vue 保持一致的文案逻辑
				if (this.generatedImages && this.generatedImages.length > 0) {
					return 'AI生成不可控，不承诺任何最终效果';
				} else if (this.generationInProgress) {
					return '您可以离开此页面，稍后在我的作品中查看';
				} else if (this.generationError) {
					return '生成失败，请重试或联系客服';
				} else {
					return '点击上方按钮开始生成';
				}
			},

			// 保存图片
			saveImage() {
				// 如果有多张图片，让用户选择要保存哪张
				if (this.generatedImages && this.generatedImages.length > 1) {
					uni.showActionSheet({
						itemList: this.generatedImages.map((_, idx) => `保存第${idx + 1}张图片`),
						success: (res) => {
							const index = res.tapIndex;
							if (index >= 0 && index < this.generatedImages.length) {
								this._saveImageToAlbum(this.generatedImages[index]);
							}
						}
					});
				} else if (this.generatedImages && this.generatedImages.length === 1) {
					// 只有一张图片则直接保存
					this._saveImageToAlbum(this.generatedImages[0]);
				}
			},
			
			// 实际保存图片到相册
			_saveImageToAlbum(imageUrl) {
				if (!imageUrl) return;

				// 确保使用完整URL
				const fullUrl = this.getImageUrl(imageUrl);

				uni.getImageInfo({
					src: fullUrl,
					success: (image) => {
						uni.saveImageToPhotosAlbum({
							filePath: image.path,
							success: () => {
								uni.showToast({
									title: '已保存到相册',
									icon: 'success'
								});
							},
							fail: (err) => {
								console.error('保存图片失败:', err);
								// 如果是因为用户拒绝授权，则引导用户开启权限
								if (err.errMsg.indexOf('auth deny') >= 0) {
									uni.showModal({
										title: '提示',
										content: '保存图片需要相册权限，请前往设置开启',
										confirmText: '去设置',
										success: (res) => {
											if (res.confirm) {
												uni.openSetting();
											}
										}
									});
								} else {
									uni.showToast({
										title: '保存图片失败',
										icon: 'none'
									});
								}
							}
						});
					},
					fail: () => {
						uni.showToast({
							title: '保存图片失败',
							icon: 'none'
						});
					}
				});
			},
			
			// 准备分享 - 设置要分享的图片
			prepareShare() {
				if (!this.generatedImages || this.generatedImages.length === 0) {
					uni.showToast({
						title: '暂无图片可分享',
						icon: 'none'
					});
					return;
				}

				// 设置当前分享的图片URL
				this.currentShareImageUrl = this.generatedImages[0];
			},
			
			// 预览图片
			previewImage(clickedUrl) {
				if (!this.generatedImages || this.generatedImages.length === 0) return;

				// 将所有图片URL转换为完整URL
				const urls = this.generatedImages.map(img => this.getImageUrl(img));
				const fullUrl = this.getImageUrl(clickedUrl);

				uni.previewImage({
					urls: urls,
					current: fullUrl,
					longPressActions: {
						itemList: ['保存图片', '分享图片'],
						success: (data) => {
							// 更新当前图片索引
							const index = data.index || 0;

							if (data.tapIndex === 0) {
								// 保存当前预览的图片
								this._saveImageToAlbum(this.generatedImages[index]);
							} else if (data.tapIndex === 1) {
								// 分享当前预览的图片
								this.currentShareImageUrl = this.generatedImages[index];
								uni.showShareMenu({
									withShareTicket: true
								});
							}
						}
					}
				});
			},

			// 自定义分享配置（仅小程序端生效）
			onShareAppMessage() {
				const imageUrl = this.currentShareImageUrl || (this.generatedImages && this.generatedImages.length > 0 ? this.generatedImages[0] : '');
				return {
					title: '哇哇哇哇哇！！！',
					path: '/pages/index/index', // 分享后点击进入首页
					imageUrl: this.getImageUrl(imageUrl)
				};
			},

			// 分享到朋友圈配置
			onShareTimeline() {
				const imageUrl = this.currentShareImageUrl || (this.generatedImages && this.generatedImages.length > 0 ? this.generatedImages[0] : '');
				return {
					title: '哇哇哇哇哇！！！',
					imageUrl: this.getImageUrl(imageUrl)
				};
			}
		}
	}
</script>

<style>
	@import url('/static/styles/icons.css');
	
	.create-container {
		background-color: #F8F8F8;
		min-height: 100vh;
		position: relative;
		width: 100%;
		box-sizing: border-box;
	}
	
	/* 滚动容器 */
	.scroll-container {
		height: 100vh;
		padding: 0 30rpx;
		box-sizing: border-box;
		width: 100%;
	}
	
	/* 顶部安全区域 */
	.safe-area {
		height: 88rpx;
	}
	
	/* 通用区块样式 */
	.section {
		margin-bottom: 40rpx;
		background-color: #FFFFFF;
		border-radius: 20rpx;
		padding: 30rpx;
		box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.08);
		position: relative;
		width: 100%;
		box-sizing: border-box;
	}
	
	/* 交替使用蓝色和紫色边框 */
	.upload-section {
		border: 4rpx solid #4A90E2;
		overflow: hidden;
	}
	
	.prompt-section {
		border: 4rpx solid #7562FF;
		margin-bottom: 20rpx;
		overflow: hidden;
	}

	.creation-mode-options {
		display: flex;
		justify-content: space-around;
		align-items: center;
		margin: 20rpx 0 40rpx 0;
		padding: 0;
		height: auto;
		background-color: transparent;
		border-radius: 0;
		border: none;
		box-shadow: none;
		position: relative;
		z-index: 1;
	}
	
	/* 移除之前的装饰线 */
	.creation-mode-options::after {
		content: none;
	}

	.mode-btn {
		display: flex;
		align-items: center;
		height: 90rpx;
		flex: 1;
		max-width: 45%;
		justify-content: center;
		background-color: #FFFFFF;
		border-radius: 45rpx;
		border: 3rpx solid #E0E0E0;
		box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.08);
		transition: all 0.3s ease;
		margin: 0 20rpx;
	}
	
	.mode-btn.active {
		background: linear-gradient(135deg, #F7F9FF 0%, #EAF2FF 100%);
		border-color: #4A90E2;
		box-shadow: 0 8rpx 16rpx rgba(74, 144, 226, 0.25);
		transform: translateY(-2rpx);
	}
	
	/* 移除原来的下划线样式 */
	.mode-btn.active::after {
		content: none;
	}
	
	.mode-btn:active {
		transform: scale(0.96);
	}
	
	.mode-icon {
		width: 44rpx;
		height: 44rpx;
		margin-right: 16rpx;
		position: relative;
	}
	
	.mode-btn.active .mode-icon {
		transform: scale(1.1);
	}

	.mode-icon.free {
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234A90E2' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M21 16V8a2 2 0 00-1-1.73l-7-4a2 2 0 00-2 0l-7 4A2 2 0 003 8v8a2 2 0 001 1.73l7 4a2 2 0 002 0l7-4A2 2 0 0021 16z'%3E%3C/path%3E%3C/svg%3E");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
		border: none;
	}

	.mode-icon.style {
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%237562FF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M21 11.5a8.38 8.38 0 01-.9 3.8 8.5 8.5 0 01-7.6 4.7 8.38 8.38 0 01-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 01-.9-3.8 8.5 8.5 0 014.7-7.6 8.38 8.38 0 013.8-.9h.5a8.48 8.48 0 018 8v.5z'%3E%3C/path%3E%3C/svg%3E");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
		border: none;
	}

	.mode-btn-text {
		font-size: 32rpx;
		color: #333333;
		font-weight: 500;
	}
	
	.mode-btn.active .mode-btn-text {
		color: #4A90E2;
		font-weight: bold;
	}
	
	.mode-btn:active {
		transform: scale(0.96);
		opacity: 0.9;
	}
	
	/* 移除右侧小箭头，改用下划线标示 */
	.mode-btn:nth-child(2)::after {
		content: none;
	}
	
	.mode-btn:active {
		transform: scale(0.96);
		opacity: 0.9;
	}
	
	/* 结果区域 */
	.result-section {
		border: 4rpx solid #4A90E2;
	}
	
	/* 添加卡通风格的装饰 */
	.upload-section::before {
		content: "";
		position: absolute;
		top: -20rpx;
		right: -20rpx;
		width: 100rpx;
		height: 100rpx;
		background-color: rgba(74, 144, 226, 0.1);
		border-radius: 50%;
		z-index: 0;
	}
	
	.prompt-section::before {
		content: "";
		position: absolute;
		top: -20rpx;
		right: -20rpx;
		width: 100rpx;
		height: 100rpx;
		background-color: rgba(117, 98, 255, 0.1);
		border-radius: 50%;
		z-index: 0;
	}
	
	.section-title-bar {
		display: flex;
		align-items: baseline;
		margin-bottom: 20rpx;
		position: relative;
		z-index: 1;
	}
	
	/* 交替使用蓝色和紫色标题 */
	.upload-section .section-title {
		color: #4A90E2;
	}
	
	.prompt-section .section-title {
		color: #7562FF;
	}
	
	.result-section .section-title {
		color: #4A90E2;
	}
	
	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		margin-right: 16rpx;
	}
	
	.section-subtitle {
		font-size: 24rpx;
		color: #999;
	}
	
	/* 上传图片区域 */
	.upload-container {
		width: 100%;
		position: relative;
		z-index: 1;
	}
	
	.upload-box.empty {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		height: 300rpx;
		background-color: #F8F9FA;
		border: 4rpx dashed #4A90E2;
		border-radius: 20rpx;
		position: relative;
		z-index: 1;
		width: 100%;
	}
	
	.upload-icon {
		width: 100rpx;
		height: 100rpx;
		margin-bottom: 20rpx;
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234A90E2' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4'%3E%3C/path%3E%3Cpolyline points='17 8 12 3 7 8'%3E%3C/polyline%3E%3Cline x1='12' y1='3' x2='12' y2='15'%3E%3C/line%3E%3C/svg%3E");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
	}
	
	.upload-overlay {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 20rpx 0;
		background: rgba(74, 144, 226, 0.8);
		text-align: center;
	}
	
	.upload-text {
		color: #FFFFFF;
		font-size: 28rpx;
		font-weight: bold;
	}
	
	.uploaded-image-container {
		width: 100%;
		height: 400rpx;
		position: relative;
		border-radius: 20rpx;
		overflow: hidden;
		box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
		border: 3rpx solid #333;
	}
	
	.preview-image {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
	
	.image-overlay {
		position: absolute;
		top: 20rpx;
		right: 20rpx;
		background-color: rgba(0, 0, 0, 0.5);
		width: 60rpx;
		height: 60rpx;
		border-radius: 30rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	.delete-icon {
		width: 32rpx;
		height: 32rpx;
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23FFFFFF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
	}
	
	/* 提示词输入区域 */
	.prompt-input-box {
		background-color: #F2F6FF;
		border-radius: 16rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
		position: relative;
		width: 100%;
		box-sizing: border-box;
	}
	
	.prompt-input {
		width: 100%;
		height: 200rpx;
		font-size: 28rpx;
		color: #333;
		line-height: 1.5;
		box-sizing: border-box;
	}
	
	.prompt-length {
		position: absolute;
		bottom: 16rpx;
		right: 20rpx;
		font-size: 24rpx;
		color: #999;
	}
	
	.prompt-tags {
		display: flex;
		flex-wrap: wrap;
		gap: 16rpx;
		width: 100%;
		box-sizing: border-box;
	}
	
	.tag {
		padding: 12rpx 24rpx;
		background-color: #FFFFFF;
		border-radius: 30rpx;
		font-size: 24rpx;
		color: #7562FF;
		border: 2rpx solid #7562FF;
		transition: all 0.3s ease;
		display: flex;
		align-items: center;
		box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	
	.tag:nth-child(2n) {
		color: #4A90E2;
		border-color: #4A90E2;
	}
	
	.tag:nth-child(2n)::before {
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234A90E2' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z'%3E%3C/path%3E%3Cline x1='7' y1='7' x2='7.01' y2='7'%3E%3C/line%3E%3C/svg%3E");
	}
	
	.tag::before {
		content: "";
		display: inline-block;
		width: 20rpx;
		height: 20rpx;
		margin-right: 8rpx;
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%237562FF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z'%3E%3C/path%3E%3Cline x1='7' y1='7' x2='7.01' y2='7'%3E%3C/line%3E%3C/svg%3E");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
		vertical-align: middle;
	}
	
	.tag:active {
		transform: scale(0.95);
	}
	
	.tag:nth-child(2n):active {
		background-color: #4A90E2;
		color: #FFFFFF;
	}
	
	.tag:active {
		background-color: #7562FF;
		color: #FFFFFF;
	}
	
	.tag:active::before, .tag:nth-child(2n):active::before {
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23FFFFFF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z'%3E%3C/path%3E%3Cline x1='7' y1='7' x2='7.01' y2='7'%3E%3C/line%3E%3C/svg%3E");
	}
	
	/* 比例选择样式 */
	.aspect-ratio-options {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 0;
		margin-bottom: 20rpx;
		padding: 16rpx 0;
		height: 80rpx; /* 固定高度，防止选中时布局跳动 */
	}
	
	.aspect-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 100%;
		border-radius: 16rpx;
		background-color: #F8F8F8;
		border: 3rpx solid #cecece;
		transition: all 0.3s ease;
		flex: 1;
		margin: 0 8rpx;
		box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
		position: relative;
		overflow: hidden;
	}
	
	.aspect-btn:first-child {
		margin-left: 0;
	}
	
	.aspect-btn:last-child {
		margin-right: 0;
	}
	
	.aspect-btn.active {
		background-color: #FFFFFF;
		border-color: #7562FF;
		box-shadow: 0 6rpx 12rpx rgba(117, 98, 255, 0.2);
	}
	
	.aspect-icon {
		width: 36rpx;
		height: 36rpx;
		margin-right: 12rpx;
		position: relative;
		border: 3rpx solid #555;
		border-radius: 6rpx;
	}
	
	.aspect-btn.active .aspect-icon {
		border-color: #7562FF;
		border-width: 4rpx;
	}
	
	/* 正方形图标 */
	.aspect-icon.square {
		width: 32rpx;
		height: 32rpx;
	}
	
	/* 竖屏图标 */
	.aspect-icon.portrait {
		width: 26rpx;
		height: 38rpx;
	}
	
	/* 横屏图标 */
	.aspect-icon.landscape {
		width: 38rpx;
		height: 26rpx;
	}
	
	.aspect-btn-text {
		font-size: 28rpx;
		color: #333;
		font-weight: 500;
	}
	
	.aspect-btn.active .aspect-btn-text {
		color: #7562FF;
		font-weight: bold;
	}
	
	/* 删除旧的尺寸选择样式 */
	.size-section {
		display: none;
	}
	
	.size-options {
		display: none;
	}
	
	.size-btn {
		display: none;
	}
	
	.size-icon {
		display: none;
	}
	
	.size-btn-text {
		display: none;
	}
	
	/* 底部操作区 */
	.bottom-action {
		display: flex;
		justify-content: space-between;
		align-items: center;
		background-color: #FFFFFF;
		border-radius: 24rpx;
		padding: 28rpx 24rpx;
		box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
		border: 4rpx solid #4A90E2;
		position: relative;
		margin-bottom: 40rpx;
		width: 100%;
		box-sizing: border-box;
		overflow: hidden;
	}
	
	.bottom-action::before {
		content: "";
		position: absolute;
		top: -30rpx;
		right: -30rpx;
		width: 100rpx;
		height: 100rpx;
		background-color: rgba(74, 144, 226, 0.1);
		border-radius: 50%;
		z-index: 0;
	}
	
	.coin-info {
		display: flex;
		align-items: center;
		background-color: #F2F6FF;
		padding: 12rpx 20rpx;
		border-radius: 30rpx;
		box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	
	.coin-icon {
		width: 60rpx;
		height: 60rpx;
		margin-right: 12rpx;
	}
	
	.coin-text {
		font-size: 28rpx;
		font-weight: bold;
		color: #7562FF;
	}
	
	.generate-btn {
		background: linear-gradient(135deg, #4A90E2 0%, #7562FF 100%);
		color: #FFFFFF;
		font-size: 30rpx;
		font-weight: bold;
		padding: 16rpx 40rpx;
		border-radius: 30rpx;
		display: flex;
		align-items: center;
		transition: transform 0.3s ease;
		border: 4rpx solid #333;
		box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
	}
	
	.generate-btn:active {
		transform: scale(0.95);
	}
	
	.generate-btn.disabled {
		background: linear-gradient(135deg, #A0A0A0 0%, #C0C0C0 100%);
		opacity: 0.7;
		box-shadow: none;
	}
	
	.btn-icon {
		margin-left: 10rpx;
		width: 24rpx;
		height: 24rpx;
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23FFFFFF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
	}
	

	
	/* 结果区域 */
	.result-section {
		margin-bottom: 60rpx;
	}
	

	

	.generating-status {
		width: 500rpx;
		height: 500rpx;
		padding: 0;
		margin: 20rpx auto 20rpx auto;
		background-color: #f8f9fa;
		border-radius: 16rpx;
		text-align: center;
		box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
		border: 4rpx solid rgba(117, 98, 255, 0.2);
		position: relative;
		overflow: hidden;
		background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
		background-size: 800px 100%;
		animation: shimmer 2s infinite linear;
	}

	/* 添加装饰元素，增强视觉效果 */
	.generating-status::after {
		content: "";
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 120rpx;
		height: 120rpx;
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%237562FF' stroke-width='1' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='2' y='2' width='20' height='20' rx='5' ry='5'%3E%3C/rect%3E%3Cpath d='M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z'%3E%3C/path%3E%3Cline x1='17.5' y1='6.5' x2='17.51' y2='6.5'%3E%3C/line%3E%3C/svg%3E");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
		opacity: 0.2;
	}

	@keyframes shimmer {
		0% {
			background-position: -800px 0;
		}
		100% {
			background-position: 800px 0;
		}
	}

	.status-tip {
		font-size: 24rpx;
		color: #999;
		text-align: center;
		margin-bottom: 30rpx;
		/* 添加淡入效果 */
		animation: fadeIn 0.5s ease-in;
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
		}
		to {
			opacity: 1;
		}
	}

	.waiting-status {
		height: 400rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		background-color: #F5F8FF;
		border-radius: 16rpx;
		border: 2rpx dashed #4A90E2;
		width: 100%;
	}
	
	.waiting-icon {
		width: 120rpx;
		height: 120rpx;
		margin-bottom: 20rpx;
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234A90E2' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpath d='M8 14s1.5 2 4 2 4-2 4-2'%3E%3C/path%3E%3Cline x1='9' y1='9' x2='9.01' y2='9'%3E%3C/line%3E%3Cline x1='15' y1='9' x2='15.01' y2='9'%3E%3C/line%3E%3C/svg%3E");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
	}
	
	.waiting-text {
		font-size: 28rpx;
		color: #4A90E2;
		text-align: center;
		padding: 0 40rpx;
		line-height: 1.5;
	}

	.error-status {
		height: 400rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		background-color: #FFF5F5;
		border-radius: 16rpx;
		border: 2rpx dashed #E53E3E;
		width: 100%;
	}

	.error-icon {
		width: 120rpx;
		height: 120rpx;
		margin-bottom: 20rpx;
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23E53E3E' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='15' y1='9' x2='9' y2='15'%3E%3C/line%3E%3Cline x1='9' y1='9' x2='15' y2='15'%3E%3C/line%3E%3C/svg%3E");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
	}

	.error-text {
		font-size: 28rpx;
		color: #E53E3E;
		text-align: center;
		padding: 0 40rpx;
		line-height: 1.5;
	}
	
	.result-box {
		margin-top: 20rpx;
		width: 100%;
	}
	
	.result-image {
		width: 100%;
		border-radius: 16rpx;
		box-shadow: 0 12rpx 24rpx rgba(0, 0, 0, 0.15);
		margin-bottom: 30rpx;
		border: 3rpx solid #333;
	}
	
	.action-row {
		display: flex;
		justify-content: space-between;
		width: 100%;
	}
	
	.action-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 20rpx;
		border-radius: 40rpx;
		flex: 1;
		transition: transform 0.3s ease;
	}
	
	.action-btn:active {
		transform: scale(0.95);
	}
	
	.save-btn {
		background-color: #F1F7FE;
		margin-right: 20rpx;
		color: #4A90E2;
		border: 2rpx solid #4A90E2;
	}
	
	.share-btn {
		background: linear-gradient(135deg, #4A90E2 0%, #7562FF 100%);
		color: #FFFFFF;
		border: 2rpx solid #333;
		/* 重置button默认样式 */
		padding: 0;
		margin: 0;
		line-height: normal;
	}

	.share-btn::after {
		border: none;
	}
	
	.action-icon {
		width: 36rpx;
		height: 36rpx;
		margin-right: 12rpx;
	}
	
	.save-icon {
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234A90E2' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z'/%3E%3Cpolyline points='17 21 17 13 7 13 7 21'/%3E%3Cpolyline points='7 3 7 8 15 8'/%3E%3C/svg%3E");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
	}
	
	.share-icon {
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23FFFFFF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='18' cy='5' r='3'/%3E%3Ccircle cx='6' cy='12' r='3'/%3E%3Ccircle cx='18' cy='19' r='3'/%3E%3Cline x1='8.59' y1='13.51' x2='15.42' y2='17.49'/%3E%3Cline x1='15.41' y1='6.51' x2='8.59' y2='10.49'/%3E%3C/svg%3E");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
	}
	
	.action-btn text {
		font-size: 28rpx;
		font-weight: 500;
	}
	
	/* 多图上传区域样式 */
	.multi-upload-container {
		margin-bottom: 20rpx;
		min-height: 260rpx; /* 减小高度 */
		width: 100%;
	}

	.multi-upload-container.integrated {
		min-height: auto;
		margin-top: 20rpx;
		margin-bottom: 20rpx;
	}
	
	.uploaded-images-scroll {
		width: 100%;
		white-space: nowrap;
		margin-bottom: 0;
		height: 180rpx; /* 减小高度 */
	}
	
	.uploaded-images-list {
		display: inline-flex;
		height: 180rpx; /* 减小高度 */
		align-items: center; /* 依靠flex布局垂直居中item */
	}
	
	.image-item {
		width: 160rpx; /* 减小宽度 */
		height: 160rpx; /* 减小高度 */
		margin-right: 16rpx;
		position: relative;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
		background-color: #FFFFFF;
	}
	
	.image-item.add-more {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		border: 2rpx dashed #4A90E2;
		background-color: #F8F9FA;
	}

	.image-item.add-more .upload-icon {
		width: 48rpx;
		height: 48rpx;
		margin-right: 0;
		margin-bottom: 8rpx;
	}

	.image-item.add-more .add-text {
		font-size: 24rpx;
	}
	
	.image-overlay {
		position: absolute;
		top: 0;
		right: 0;
		background-color: rgba(0, 0, 0, 0.5);
		width: 48rpx;
		height: 48rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		border-bottom-left-radius: 16rpx;
	}
	
	.delete-icon {
		width: 32rpx;
		height: 32rpx;
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23FFFFFF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='3 6 5 6 21 6'%3E%3C/polyline%3E%3Cpath d='M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2'%3E%3C/path%3E%3C/svg%3E");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
	}
	
	.image-index {
		position: absolute;
		bottom: 0;
		left: 0;
		background: linear-gradient(135deg, #4A90E2 0%, #7562FF 100%);
		color: #FFFFFF;
		padding: 4rpx 16rpx;
		font-size: 24rpx;
		font-weight: bold;
		border-top-right-radius: 16rpx;
	}
	
	.preview-image {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
	
	.upload-icon.large {
		width: 120rpx;
		height: 120rpx;
	}
	
	.add-text {
		font-size: 24rpx;
		color: #4A90E2;
		margin-top: 10rpx;
	}
	
	/* 空状态上传框 */
	.upload-box.empty {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		box-sizing: border-box;
		height: 260rpx; /* 减小高度 */
		background-color: #F8F9FA;
		border: 4rpx dashed #4A90E2;
		border-radius: 24rpx;
		box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.08);
		width: 100%;
	}

	.upload-box-compact {
		display: flex;
		align-items: center;
		padding: 20rpx;
		background-color: #F2F6FF;
		border-radius: 16rpx;
		border: 2rpx dashed #4A90E2;
		transition: background-color 0.3s;
	}

	.upload-box-compact:active {
		background-color: #E6EEFF;
	}

	.upload-icon-compact {
		width: 40rpx;
		height: 40rpx;
		margin-right: 16rpx;
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234A90E2' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='3' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='12' y1='8' x2='12' y2='16'%3E%3C/line%3E%3Cline x1='8' y1='12' x2='16' y2='12'%3E%3C/line%3E%3C/svg%3E");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
	}

	.upload-text-compact {
		font-size: 28rpx;
		color: #4A90E2;
		font-weight: 500;
	}
	
	/* 删除不再需要的上传图片区域样式 */
	.uploaded-image-container {
		display: none;
	}
	
	/* 结果图片列表样式 */
	.result-images-list {
		display: flex;
		flex-direction: column;
		gap: 16rpx; /* 图片之间的间距 */
	}

	.result-image-item {
		width: 100%;
	}
	
	.result-image {
		width: 100%;
		border-radius: 16rpx;
		box-shadow: 0 12rpx 24rpx rgba(0, 0, 0, 0.15);
		border: 3rpx solid #333;
	}
</style>
