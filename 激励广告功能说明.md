# 激励广告功能接入说明

## 功能概述

为 WowPic 支付页面接入了激励视频广告功能，用户观看完整广告后可获得 15 哇图币奖励，每日限制观看 3 次。

## 实现内容

### 前端实现 (WowPic/pages/pay/pay.vue)

1. **广告实例初始化**
   - 在页面 `onLoad` 时创建激励视频广告实例
   - 使用广告位ID: `adunit-b8fe48de4a45e35e`
   - 监听广告加载、错误、关闭事件

2. **用户交互**
   - 点击"观看激励广告"按钮触发广告播放
   - 动态显示今日观看次数 (x/3)
   - 完整观看后自动发放奖励

3. **状态管理**
   - 实时更新观看次数显示
   - 达到每日上限时禁用按钮
   - 页面卸载时清理广告实例

### 后端实现 (WowPicServer/utils/ad.py)

1. **API 接口**
   - `GET /wowpic/ad/watch-count` - 获取今日观看次数
   - `POST /wowpic/ad/reward` - 发放广告观看奖励

2. **业务逻辑**
   - 每日观看次数限制 (3次)
   - 防重复领取机制
   - 哇图币余额更新
   - 交易流水记录

3. **数据模型**
   - 使用现有的 `CoinTransaction` 表记录流水
   - 交易来源类型: `VIDEO_AD`
   - 奖励金额: 15 哇图币

## 配置参数

```javascript
// 前端配置
adUnitId: 'adunit-b8fe48de4a45e35e'  // 微信小程序广告位ID
```

```python
# 后端配置
VIDEO_AD_REWARD = 15              # 每次观看奖励
MAX_VIDEO_AD_PER_DAY = 3         # 每日观看上限
```

## 测试步骤

### 1. 后端测试

启动后端服务后，可以使用以下 API 进行测试：

```bash
# 获取观看次数
curl -X GET "http://localhost:8000/wowpic/ad/watch-count" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 发放奖励
curl -X POST "http://localhost:8000/wowpic/ad/reward" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"adType": "video", "reward": 15}'
```

### 2. 前端测试

1. **开发环境测试**
   - 在微信开发者工具中打开项目
   - 切换基础库版本以支持广告预览
   - 进入支付页面测试广告功能

2. **真机测试**
   - 需要在微信小程序后台配置广告位
   - 上传体验版进行真机测试

## 注意事项

### 开发环境
- 微信开发者工具可能无法预览广告，需要切换基础库版本
- 建议在真机上进行完整测试

### 生产环境
1. **广告位申请**
   - 需要在微信小程序后台申请激励视频广告权限
   - 替换示例广告位ID为真实的广告位ID

2. **防刷机制**
   - 后端已实现每日观看次数限制
   - 可根据需要增加更严格的防刷策略

3. **错误处理**
   - 广告加载失败时的用户提示
   - 网络异常时的重试机制
   - 奖励发放失败的补偿机制

## 数据库影响

新增的交易记录会写入 `coin_transactions` 表：
- `source`: `VIDEO_AD`
- `change`: `15` (正数表示收入)
- `remark`: "观看激励视频广告奖励"

## 扩展建议

1. **广告类型扩展**
   - 支持更多广告类型 (横幅广告、插屏广告等)
   - 不同广告类型给予不同奖励

2. **奖励策略优化**
   - 连续观看奖励递增
   - 特殊活动期间奖励翻倍
   - VIP用户额外奖励

3. **数据统计**
   - 广告观看率统计
   - 用户行为分析
   - 收益数据报表

## 相关文件

- 前端: `WowPic/pages/pay/pay.vue`
- 后端: `WowPicServer/utils/ad.py`
- 路由注册: `WowPicServer/main.py`
- 数据模型: `WowPicServer/database/models.py`
