# WowPic 多图生成（方案 A）详细设计文档

> 作者：XXX &nbsp;&nbsp; 日期：2025-06-XX

---

## 1. 目标
1. 支持 AIHubMix `n = 1/2/4` 一次生成多张图片。
2. 支持任务进程重启后继续运行（结合 Celery 队列已在另一文档说明）。
3. 数据库存储符合第三范式：`generation` 与多张图片一对多关系，便于后续扩展评分 / 标记等。

---

## 2. 数据库设计

### 2.1 现状
`generation` 表主要字段（简化）：
| 字段 | 类型 | 说明 |
| --- | --- | --- |
| id | BIGINT PK | 生成记录 ID |
| user_id | BIGINT | 用户 |
| style_id | INT | 风格 |
| prompt | VARCHAR(255) | 用户提示词 |
| source_image_url | VARCHAR(255) | 源图 |
| generated_image_url | VARCHAR(255) | **单张结果 URL** |
| status | ENUM | PENDING / SUCCESS / FAILED |
| task_id | VARCHAR(64) | Celery 任务 ID |
| created_at / updated_at | DATETIME | |

### 2.2 方案 A 调整
1. **删除** `generated_image_url` 字段（处于开发期可直接删）。
2. **新增** 可选统计字段：
   * `images_count` INT DEFAULT 0 —— 生成张数，方便统计。  
   * `completed_at` DATETIME —— 全部图片生成完毕的时间。

```sql
-- 修改 generation 表
ALTER TABLE generation 
    DROP COLUMN generated_image_url,
    ADD COLUMN images_count INT DEFAULT 0 AFTER prompt,
    ADD COLUMN completed_at DATETIME NULL AFTER updated_at;
```

3. **新建** `generation_images` 表（一对多）：

```sql
CREATE TABLE generation_images (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    generation_id BIGINT NOT NULL,
    image_url VARCHAR(255) NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_gen_id (generation_id),
    CONSTRAINT fk_gen_images FOREIGN KEY (generation_id)
        REFERENCES generation(id) ON DELETE CASCADE
);
```

---

## 3. 后端改造

### 3.1 Pydantic / SQLAlchemy 模型
```python
class Generation(Base):
    __tablename__ = "generation"
    id = Column(BigInteger, primary_key=True)
    user_id = Column(BigInteger, nullable=False)
    style_id = Column(Integer, nullable=False)
    prompt = Column(String(255))
    source_image_url = Column(String(255))
    images_count = Column(Integer, default=0)
    status = Column(Enum("PENDING", "SUCCESS", "FAILED"))
    task_id = Column(String(64))
    created_at = Column(DateTime, default=datetime.now)
    completed_at = Column(DateTime)
    images = relationship("GenerationImage", back_populates="generation")

class GenerationImage(Base):
    __tablename__ = "generation_images"
    id = Column(BigInteger, primary_key=True)
    generation_id = Column(BigInteger, ForeignKey("generation.id"))
    image_url = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=datetime.now)
    generation = relationship("Generation", back_populates="images")
```

### 3.2 生成任务逻辑
1. 提交接口 `/generate/{style_id}/async`
   * 新增参数 `n:int = 1`，校验 `1 <= n <= 4`。
   * 创建 `Generation` 记录：`images_count = n`。
   * 推送 Celery 任务（task_id 写入）。

2. Celery Worker `generate_image_task`
   * 读取 `n`，循环调用 StyleHandler（如 `GhibliV1Style.generate`）一次性获取多个 URL，或多次调用得到多张。
   * 对每个 URL 插入 `GenerationImage`。  
   * 全部成功后将 `generation.status = "SUCCESS"`，`completed_at = now()`。

```python
@celery_app.task(bind=True, acks_late=True, max_retries=3)
def generate_image_task(self, generation_id: int):
    gen = db.get(Generation, generation_id)
    handler = get_handler(gen.style_id)
    urls: list[str] = handler.generate({ ... , "n": gen.images_count })
    for url in urls:
        db.add(GenerationImage(generation_id=gen.id, image_url=url))
    gen.status = "SUCCESS"
    gen.completed_at = datetime.now()
    db.commit()
```

3. **状态接口** `/generate/status/{task_id}` 修改返回：
```json
{
  "status": "SUCCESS",
  "generated_image_urls": ["/static/...1.png", "/static/...2.png"],
  "images_count": 2
}
```

### 3.3 部分成功与容错处理
在调用第三方模型时偶尔会出现 `n` 与最终拿到的图片数量不一致的情况，需要容错。

1. **结果校验**  
   Celery 任务结束前执行：
   ```python
   real_count = db.query(GenerationImage).filter_by(generation_id=gen.id).count()
   if real_count != gen.images_count:
       gen.status = "PARTIAL"  # 新增枚举值
       gen.missing_count = gen.images_count - real_count  # 可选字段
   else:
       gen.status = "SUCCESS"
   db.commit()
   ```

2. **数据库字段调整**  
   ```sql
   ALTER TABLE generation
       ADD COLUMN missing_count INT DEFAULT 0,
       MODIFY COLUMN status ENUM('PENDING','SUCCESS','FAILED','PARTIAL');
   ```

3. **前端提示**  
   收到 `status = PARTIAL` 时：
   * 正常展示已生成的图片。
   * Toast / Banner：`部分图片生成失败，已为你展示成功部分`。

4. **计费逻辑**  
   可按 `real_count` 实际扣费，或返还差额哇图币。业务层根据 `missing_count` 决策。

5. **补偿任务（可选）**  
   定时脚本扫描 `status = PARTIAL` 的记录，再次调用生成接口尝试补齐缺失图片，提高成功率。

> 以上容错设计保证即使生成接口偶发缺图，也不会影响主流程与统计，并方便后期迭代。

---

## 4. 前端改造

| 页面 | 变动 | 说明 |
| --- | --- | --- |
| generate.vue | • 增加 n 选择（Stepper / 下拉）<br>• 结果区域改为列表 / 轮播<br>• 提交时携带 `n` | |
| profile.vue | • 作品卡片只展示首图缩略图<br>• 点击进入详情页展示全部图片 | |
| 作品详情页（新） | • swiper 显示多图<br>• 支持保存 / 分享单张 | |

---

## 5. 数据迁移 & 回滚
* 目前测试库，可直接 `ALTER` + `DROP`。
* 若需回滚，只需：
  1. 恢复 `generated_image_url`；
  2. 删除 `generation_images` 表。

---

## 6. 测试用例
| 编号 | 场景 | 断言 |
| --- | --- | --- |
| T-01 | n=1 正常生成 | status=SUCCESS 且 images_count=1；`generation_images` 1 行 |
| T-02 | n=4 生成 | `generation_images` =4 行，前端轮播 4 张 |
| T-03 | 进程重启后 Celery 继续 | 任务最终成功完成 |
| T-04 | 部分 URL 解析失败 | 任务标记 FAILED，错误信息记录 |

---

## 7. 开发排期（示例）
| Day | 任务 |
| --- | --- |
| D1 | DB 迁移脚本 & ORM 模型调整 |
| D2 | Celery 队列接入、单张任务迁移 |
| D3 | StyleHandler 支持 n>1；多图插入逻辑 |
| D4 | API 参数 & 状态返回调整；单元测试 |
| D5 | 前端 generate.vue 多图 UI；轮播组件 |
| D6 | 作品详情页 + profile 列表适配 |
| D7 | QA / Bugfix & 上线 |

---

## 8. FAQ
1. **一定要删除 `generated_image_url` 吗？**  
   若希望保持向前兼容，可暂留字段并改为 NULL；正式上线后再彻底移除。
2. **AIHubMix 是否一次就返回多图 URL？**  
   对 `chat.completions` 接口，返回文本里会有多段 `![...](url)`，解析即可。
3. **是否支持并发下载/存储？**  
   Celery Worker 内可 async 并发 `httpx` 下载到本地，再上传 OSS。

---

> 如有疑问请联系 @backend-team 