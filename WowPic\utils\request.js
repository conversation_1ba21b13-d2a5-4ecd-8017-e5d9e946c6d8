/**
 * 请求封装工具
 * 统一处理API前缀、请求头、Token携带、错误码处理和loading等逻辑
 */

// 直接使用 uni-app 提供的全局 getApp()，无需重新定义

// 导入配置文件
import { baseUrl as configBaseUrl } from './config.js';

/**
 * 请求封装
 * @param {Object} options - 请求选项
 * @param {string} options.url - 接口地址（无需添加前缀）
 * @param {string} [options.method='GET'] - 请求方法
 * @param {Object} [options.data] - 请求数据
 * @param {boolean} [options.showLoading=true] - 是否显示加载提示
 * @param {boolean} [options.hideErrorTips=false] - 是否隐藏错误提示
 * @returns {Promise} 返回Promise对象
 */
const request = function(options) {
	// 默认配置
	const config = {
		baseUrl: getApp().globalData.apiBaseUrl || configBaseUrl,
		// 默认不显示加载提示，只有明确指定时才显示
		showLoading: options.showLoading === true,
		hideErrorTips: options.hideErrorTips === true,
		...options
	};
	
	// 拼接完整URL
	if (config.url.indexOf('http') !== 0) {
		config.url = config.baseUrl + config.url;
	}
	
	// 显示加载提示
	if (config.showLoading) {
		uni.showLoading({
			title: '加载中...',
			mask: true
		});
	}
	
	// 获取Token
	const token = uni.getStorageSync('token');
	
	// 构建请求头
	config.header = {
		'Content-Type': 'application/json',
		...config.header
	};
	
	// 如果有Token，添加到请求头
	if (token) {
		config.header['Authorization'] = `Bearer ${token}`;
	}
	
	// 发起请求并返回Promise
	return new Promise((resolve, reject) => {
		uni.request({
			url: config.url,
			data: config.data,
			method: config.method || 'GET',
			header: config.header,
			success: function(res) {
				// 隐藏加载提示
				if (config.showLoading) {
					uni.hideLoading();
				}
				
				// 请求成功
				if (res.statusCode >= 200 && res.statusCode < 300) {
					resolve(res.data);
				} 
				// 未授权
				else if (res.statusCode === 401) {
					// Token无效或过期，清除本地Token
					uni.removeStorageSync('token');
					getApp().globalData.isLoggedIn = false;
					
					if (!config.hideErrorTips) {
						uni.showToast({
							title: '登录已过期，请重新登录',
							icon: 'none',
							duration: 2000
						});
					}
					
					// 5秒后尝试重新登录
					setTimeout(() => {
						getApp().autoLogin();
					}, 2000);
					
					reject(res);
				}
				// 哇图币不足
				else if (res.statusCode === 402) {
					if (!config.hideErrorTips) {
						uni.showModal({
							title: '哇图币不足',
							content: res.data.detail || '您的哇图币余额不足，无法完成此操作',
							confirmText: '去获取',
							cancelText: '取消',
							success: (result) => {
								if (result.confirm) {
									// 跳转到支付页面获取哇图币
									uni.navigateTo({
										url: '/pages/pay/pay'
									});
								}
							}
						});
					}
					reject(res);
				}
				// 其他错误
				else {
					if (!config.hideErrorTips) {
						uni.showToast({
							title: res.data.detail || '请求失败',
							icon: 'none',
							duration: 2000
						});
					}
					reject(res);
				}
			},
			fail: function(err) {
				// 隐藏加载提示
				if (config.showLoading) {
					uni.hideLoading();
				}
				
				if (!config.hideErrorTips) {
					uni.showToast({
						title: '网络请求失败',
						icon: 'none',
						duration: 2000
					});
				}
				
				reject(err);
			}
		});
	});
};

// 快捷方法
const get = function(url, data, options = {}) {
	return request({
		url,
		data,
		method: 'GET',
		...options
	});
};

const post = function(url, data, options = {}) {
	return request({
		url,
		data,
		method: 'POST',
		...options
	});
};

// 添加PUT方法
const put = function(url, data, options = {}) {
	return request({
		url,
		data,
		method: 'PUT',
		...options
	});
};

// 导出请求方法
export default {
	request,
	get,
	post,
	put,
	baseUrl: configBaseUrl // 导出baseUrl便于上传文件等场景使用
}; 