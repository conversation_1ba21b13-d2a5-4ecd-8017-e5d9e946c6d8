# WowPic 赠送系统与Web端扩展开发计划

## 1. 项目概述

### 1.1 背景
由于iOS系统限制虚拟支付，需要开发赠送功能作为替代方案，同时为后续Web端扩展做准备。

### 1.2 核心目标
1. **赠送功能**：用户可以购买后赠送哇图币给好友
2. **兑换码系统**：支持兑换码生成、分享、兑换
3. **分享裂变**：分享获得奖励机制
4. **Web端基础**：扩展认证系统支持Web端登录
5. **Web端充值**：Web端用户可直接充值到账

### 1.3 开发阶段规划
- **阶段一**：数据库扩展与兑换码系统
- **阶段二**：赠送功能与小程序端界面
- **阶段三**：分享裂变系统
- **阶段四**：Web端认证系统扩展
- **阶段五**：Web端界面与充值功能

## 2. 数据库设计扩展

### 2.1 新增表结构

#### 2.1.1 兑换码表 (gift_codes)
```sql
CREATE TABLE gift_codes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(16) UNIQUE NOT NULL COMMENT '兑换码',
    coins INT NOT NULL COMMENT '可兑换哇图币数量',
    giver_user_id INT NOT NULL COMMENT '赠送者用户ID',
    receiver_user_id INT NULL COMMENT '接收者用户ID',
    status ENUM('UNUSED', 'USED', 'EXPIRED') DEFAULT 'UNUSED' COMMENT '状态',
    source_type ENUM('PURCHASE', 'ACTIVITY', 'MANUAL') DEFAULT 'PURCHASE' COMMENT '来源类型',
    source_order_id BIGINT NULL COMMENT '关联订单ID',
    expires_at DATETIME NULL COMMENT '过期时间',
    used_at DATETIME NULL COMMENT '使用时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_code (code),
    INDEX idx_giver (giver_user_id),
    INDEX idx_receiver (receiver_user_id),
    INDEX idx_status (status),
    FOREIGN KEY (giver_user_id) REFERENCES users(id),
    FOREIGN KEY (receiver_user_id) REFERENCES users(id)
);
```

#### 2.1.2 分享奖励表 (share_rewards)
```sql
CREATE TABLE share_rewards (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    sharer_user_id INT NOT NULL COMMENT '分享者用户ID',
    share_code VARCHAR(32) UNIQUE NOT NULL COMMENT '分享码',
    visitor_openid VARCHAR(128) NULL COMMENT '访客openid',
    visitor_user_id INT NULL COMMENT '访客用户ID（注册后）',
    reward_coins INT DEFAULT 5 COMMENT '奖励金币数',
    status ENUM('PENDING', 'REWARDED', 'EXPIRED') DEFAULT 'PENDING',
    rewarded_at DATETIME NULL COMMENT '奖励发放时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_sharer (sharer_user_id),
    INDEX idx_share_code (share_code),
    INDEX idx_visitor (visitor_user_id),
    FOREIGN KEY (sharer_user_id) REFERENCES users(id),
    FOREIGN KEY (visitor_user_id) REFERENCES users(id)
);
```

#### 2.1.3 Web端会话表 (web_sessions)
```sql
CREATE TABLE web_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    session_token VARCHAR(64) UNIQUE NOT NULL COMMENT '会话token',
    qr_token VARCHAR(64) UNIQUE NULL COMMENT '二维码登录token',
    user_id INT NULL COMMENT '关联用户ID',
    login_type ENUM('QR', 'PHONE') NOT NULL COMMENT '登录方式',
    phone VARCHAR(20) NULL COMMENT '手机号（手机登录时）',
    status ENUM('PENDING', 'CONFIRMED', 'EXPIRED') DEFAULT 'PENDING',
    expires_at DATETIME NOT NULL COMMENT '过期时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_session_token (session_token),
    INDEX idx_qr_token (qr_token),
    INDEX idx_user (user_id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### 2.2 现有表扩展

#### 2.2.1 用户表 (users) 新增字段
```sql
ALTER TABLE users ADD COLUMN share_code VARCHAR(16) UNIQUE NULL COMMENT '个人分享码';
ALTER TABLE users ADD COLUMN total_shared_rewards INT DEFAULT 0 COMMENT '累计分享奖励';
ALTER TABLE users ADD COLUMN one_yuan_used BOOLEAN DEFAULT FALSE COMMENT '是否使用过1元特惠';
```

#### 2.2.2 充值订单表 (recharge_orders) 新增字段
```sql
ALTER TABLE recharge_orders ADD COLUMN platform VARCHAR(10) DEFAULT 'WX' COMMENT '充值平台';
ALTER TABLE recharge_orders ADD COLUMN gift_code_id BIGINT NULL COMMENT '关联兑换码ID';
ALTER TABLE recharge_orders ADD FOREIGN KEY (gift_code_id) REFERENCES gift_codes(id);
```

## 3. 后端API设计

### 3.1 兑换码相关接口

#### 3.1.1 购买兑换码
```
POST /wowpic/gift/purchase
Authorization: Bearer <token>
Content-Type: application/json

{
    "coins": 100,           // 兑换码金币数量
    "quantity": 1,          // 购买数量
    "expires_days": 30      // 有效期天数（可选，默认30天）
}

Response:
{
    "success": true,
    "codes": ["ABC123DEF456"],
    "total_cost": 1.0,      // 总花费人民币
    "order_id": "xxx"
}
```

#### 3.1.2 兑换码兑换
```
POST /wowpic/gift/redeem
Authorization: Bearer <token>
Content-Type: application/json

{
    "code": "ABC123DEF456"
}

Response:
{
    "success": true,
    "coins": 100,
    "new_balance": 150,
    "giver_nickname": "哇兔兔AB12"
}
```

#### 3.1.3 我的兑换码列表
```
GET /wowpic/gift/my-codes?type=sent&page=1&limit=10
Authorization: Bearer <token>

Response:
{
    "items": [
        {
            "id": 1,
            "code": "ABC123DEF456",
            "coins": 100,
            "status": "UNUSED",
            "receiver_nickname": null,
            "expires_at": "2024-02-01T00:00:00Z",
            "created_at": "2024-01-01T00:00:00Z"
        }
    ],
    "total": 5,
    "page": 1,
    "limit": 10
}
```

### 3.2 分享奖励接口

#### 3.2.1 生成分享链接
```
POST /wowpic/share/generate
Authorization: Bearer <token>

Response:
{
    "share_code": "SHARE123456",
    "share_url": "https://wowpic.com?share=SHARE123456",
    "qr_code_url": "https://api.wowpic.com/static/qr/SHARE123456.png"
}
```

#### 3.2.2 处理分享访问
```
POST /wowpic/share/visit
Content-Type: application/json

{
    "share_code": "SHARE123456",
    "visitor_openid": "oXXXXXXXXXXXXXXXX"  // 可选，未登录用户为空
}

Response:
{
    "success": true,
    "reward_pending": true,
    "message": "访问记录成功，奖励将在用户注册后发放"
}
```

### 3.3 Web端认证接口

#### 3.3.1 生成登录二维码
```
POST /wowpic/web/auth/qr-generate

Response:
{
    "qr_token": "QR123456789",
    "qr_url": "https://api.wowpic.com/static/qr/login/QR123456789.png",
    "expires_in": 300
}
```

#### 3.3.2 小程序端确认登录
```
POST /wowpic/web/auth/qr-confirm
Authorization: Bearer <token>
Content-Type: application/json

{
    "qr_token": "QR123456789"
}

Response:
{
    "success": true,
    "message": "登录确认成功"
}
```

#### 3.3.3 Web端轮询登录状态
```
GET /wowpic/web/auth/qr-status?token=QR123456789

Response:
{
    "status": "confirmed",  // pending/confirmed/expired
    "web_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user_info": {
        "id": 123,
        "nickname": "哇兔兔AB12",
        "avatar_url": "...",
        "coins": 150
    }
}
```

#### 3.3.4 手机号登录（备选方案）
```
POST /wowpic/web/auth/phone-login
Content-Type: application/json

{
    "phone": "13800138000",
    "verify_code": "123456"
}

Response:
{
    "success": true,
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "is_new_user": false,
    "user_info": {...}
}
```

## 4. 前端开发计划

### 4.1 小程序端新增页面

#### 4.1.1 兑换码购买页面 (`pages/gift/purchase.vue`)
- 选择兑换码金币数量
- 选择购买数量
- 显示总价格
- 支付流程（复用现有支付逻辑）
- iOS用户引导文案

#### 4.1.2 兑换码管理页面 (`pages/gift/manage.vue`)
- 我送出的兑换码列表
- 我收到的兑换码列表
- 兑换码状态显示
- 分享兑换码功能

#### 4.1.3 兑换码兑换页面 (`pages/gift/redeem.vue`)
- 输入兑换码
- 扫码兑换（调用扫码API）
- 兑换结果显示
- 感谢赠送者界面

#### 4.1.4 分享奖励页面 (`pages/share/reward.vue`)
- 生成个人分享码
- 分享记录列表
- 奖励统计
- 分享到微信功能

### 4.2 Web端开发

#### 4.2.1 技术栈选择
- **框架**: Vue 3 + Vite
- **UI库**: Element Plus 或 Ant Design Vue
- **状态管理**: Pinia
- **路由**: Vue Router
- **HTTP**: Axios

#### 4.2.2 核心页面
1. **登录页面** - 二维码登录/手机号登录
2. **首页** - 风格展示（复用小程序逻辑）
3. **生成页面** - AI图片生成
4. **个人中心** - 用户信息、作品管理
5. **充值页面** - 支付宝/微信H5支付

## 5. 开发时间规划

### 阶段一：数据库与核心API（1周）
- [ ] 数据库表结构创建
- [ ] 兑换码相关API开发
- [ ] 分享奖励API开发
- [ ] 单元测试编写

### 阶段二：小程序端功能（1.5周）
- [ ] 兑换码购买页面
- [ ] 兑换码管理页面
- [ ] 兑换码兑换页面
- [ ] 分享功能集成
- [ ] iOS用户体验优化

### 阶段三：Web端认证系统（1周）
- [ ] 认证系统扩展
- [ ] 二维码登录实现
- [ ] 手机号登录实现
- [ ] Web端支付接口

### 阶段四：Web端界面开发（2周）
- [ ] 项目搭建与配置
- [ ] 登录页面开发
- [ ] 核心功能页面开发
- [ ] 响应式适配
- [ ] 测试与优化

### 阶段五：测试与上线（0.5周）
- [ ] 功能测试
- [ ] 兼容性测试
- [ ] 性能优化
- [ ] 文档完善

**总计：6周**

## 6. 技术风险与解决方案

### 6.1 主要风险
1. **iOS支付限制** - 通过兑换码绕过
2. **Web端支付集成** - 使用成熟的支付SDK
3. **跨平台数据同步** - 基于现有JWT系统
4. **二维码登录安全性** - 设置合理过期时间和加密

### 6.2 性能考虑
1. **兑换码生成算法** - 使用高效的随机算法
2. **分享统计** - 异步处理，避免阻塞
3. **Web端缓存策略** - 合理使用浏览器缓存

## 7. 测试计划

### 7.1 功能测试
- 兑换码购买、兑换流程
- 分享奖励机制
- Web端登录流程
- 跨平台数据一致性

### 7.2 兼容性测试
- iOS/Android小程序
- 主流浏览器Web端
- 不同网络环境

### 7.3 安全测试
- 兑换码重复使用防护
- 分享奖励刷量防护
- Web端登录安全性

## 8. 上线部署

### 8.1 小程序端
- 微信小程序审核
- 版本发布管理
- 用户引导文案

### 8.2 Web端
- 域名配置
- SSL证书
- CDN加速
- 监控告警

## 9. 代码实现要点

### 9.1 后端模型定义 (database/models.py)

#### 9.1.1 兑换码状态枚举
```python
class GiftCodeStatus(enum.Enum):
    UNUSED = "UNUSED"
    USED = "USED"
    EXPIRED = "EXPIRED"

class GiftCodeSource(enum.Enum):
    PURCHASE = "PURCHASE"
    ACTIVITY = "ACTIVITY"
    MANUAL = "MANUAL"
```

#### 9.1.2 兑换码模型
```python
class GiftCode(Base):
    __tablename__ = 'gift_codes'

    id = Column(BIGINT, primary_key=True, autoincrement=True)
    code = Column(String(16), unique=True, nullable=False, index=True)
    coins = Column(Integer, nullable=False)
    giver_user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    receiver_user_id = Column(Integer, ForeignKey('users.id'), nullable=True)
    status = Column(Enum(GiftCodeStatus), default=GiftCodeStatus.UNUSED, index=True)
    source_type = Column(Enum(GiftCodeSource), default=GiftCodeSource.PURCHASE)
    source_order_id = Column(BIGINT, nullable=True)
    expires_at = Column(DateTime, nullable=True)
    used_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.now)

    # 关系
    giver = relationship("User", foreign_keys=[giver_user_id], backref="sent_gift_codes")
    receiver = relationship("User", foreign_keys=[receiver_user_id], backref="received_gift_codes")
```

### 9.2 核心业务逻辑

#### 9.2.1 兑换码生成算法
```python
import secrets
import string

def generate_gift_code() -> str:
    """生成16位兑换码，格式：XXXX-XXXX-XXXX-XXXX"""
    chars = string.ascii_uppercase + string.digits
    # 排除容易混淆的字符
    chars = chars.replace('0', '').replace('O', '').replace('1', '').replace('I', '')

    while True:
        code_parts = []
        for _ in range(4):
            part = ''.join(secrets.choice(chars) for _ in range(4))
            code_parts.append(part)

        code = '-'.join(code_parts)

        # 检查数据库中是否已存在
        if not db.query(GiftCode).filter(GiftCode.code == code).first():
            return code
```

#### 9.2.2 兑换码购买逻辑
```python
async def purchase_gift_codes(
    db: Session,
    user: User,
    coins: int,
    quantity: int = 1,
    expires_days: int = 30
) -> List[str]:
    """购买兑换码"""

    # 计算总价格（按现有充值比例）
    total_cost_cny = calculate_gift_code_price(coins, quantity)

    # 创建充值订单
    order = RechargeOrder(
        user_id=user.id,
        out_trade_no=uuid.uuid4().hex[:32],
        coins=0,  # 兑换码不直接到账
        amount_cny=int(total_cost_cny * 100),  # 转为分
        platform="WX",
        status=RechargeOrderStatus.PENDING
    )
    db.add(order)
    db.flush()

    # 生成兑换码
    codes = []
    expires_at = datetime.now() + timedelta(days=expires_days)

    for _ in range(quantity):
        code = generate_gift_code()
        gift_code = GiftCode(
            code=code,
            coins=coins,
            giver_user_id=user.id,
            source_type=GiftCodeSource.PURCHASE,
            source_order_id=order.id,
            expires_at=expires_at
        )
        db.add(gift_code)
        codes.append(code)

    db.commit()
    return codes, order.out_trade_no
```

#### 9.2.3 兑换码兑换逻辑
```python
async def redeem_gift_code(db: Session, user: User, code: str) -> dict:
    """兑换兑换码"""

    # 查找兑换码
    gift_code = db.query(GiftCode).filter(GiftCode.code == code).first()

    if not gift_code:
        raise HTTPException(status_code=404, detail="兑换码不存在")

    if gift_code.status != GiftCodeStatus.UNUSED:
        raise HTTPException(status_code=400, detail="兑换码已使用")

    if gift_code.expires_at and gift_code.expires_at < datetime.now():
        gift_code.status = GiftCodeStatus.EXPIRED
        db.commit()
        raise HTTPException(status_code=400, detail="兑换码已过期")

    if gift_code.giver_user_id == user.id:
        raise HTTPException(status_code=400, detail="不能兑换自己的兑换码")

    # 执行兑换
    gift_code.receiver_user_id = user.id
    gift_code.status = GiftCodeStatus.USED
    gift_code.used_at = datetime.now()

    # 增加用户金币
    user.coins += gift_code.coins

    # 记录流水
    db.add(CoinTransaction(
        user_id=user.id,
        change=gift_code.coins,
        balance=user.coins,
        source=CoinTransactionSource.GIFT,
        source_id=gift_code.id,
        remark=f"兑换码兑换: {code}"
    ))

    db.commit()

    return {
        "coins": gift_code.coins,
        "new_balance": user.coins,
        "giver_nickname": gift_code.giver.nickname
    }
```

### 9.3 Web端认证扩展

#### 9.3.1 认证系统扩展
```python
# 在 utils/auth.py 中扩展
async def get_web_user_info(code: str, login_type: str):
    """处理Web端用户认证"""

    if login_type == "qr":
        # 二维码登录
        session = db.query(WebSession).filter(
            WebSession.qr_token == code,
            WebSession.status == "CONFIRMED",
            WebSession.expires_at > datetime.now()
        ).first()

        if not session:
            raise Exception("二维码登录失败或已过期")

        return {
            "user_id": f"web_qr_{session.user_id}",
            "actual_user_id": session.user_id,
            "unionid": None
        }

    elif login_type == "phone":
        # 手机号登录
        phone, verify_code = code.split(":", 1)

        # 验证短信验证码（需要接入短信服务）
        if not await verify_sms_code(phone, verify_code):
            raise Exception("验证码错误")

        return {
            "user_id": f"web_phone_{phone}",
            "phone": phone,
            "unionid": None
        }

    else:
        raise Exception("不支持的Web登录类型")

# 扩展主登录逻辑
async def login_or_register_logic(db: Session, code: str, platform: str, login_type: str = None):
    """扩展后的登录逻辑"""

    if platform == "WX":
        platform_data = await get_wx_openid(code)
        platform_uid = platform_data.get("openid")
        platform_unionid = platform_data.get("unionid")

    elif platform == "WEB":
        platform_data = await get_web_user_info(code, login_type)
        platform_uid = platform_data.get("user_id")
        platform_unionid = platform_data.get("unionid")

        # 如果是二维码登录，直接返回已存在用户
        if login_type == "qr" and "actual_user_id" in platform_data:
            user = db.query(User).filter(User.id == platform_data["actual_user_id"]).first()
            if user:
                user.last_login_at = datetime.now()
                db.commit()
                token = create_access_token({"user_id": user.id})
                return {"token": token, "isNewUser": False}

    else:
        raise Exception(f"不支持的平台类型: {platform}")

    # 后续逻辑保持不变...
```

### 9.4 小程序端关键组件

#### 9.4.1 兑换码输入组件
```vue
<!-- components/GiftCodeInput.vue -->
<template>
  <view class="gift-code-input">
    <view class="input-group">
      <input
        v-model="codeValue"
        placeholder="请输入兑换码"
        maxlength="19"
        @input="formatCode"
        class="code-input"
      />
      <button @click="scanCode" class="scan-btn">扫码</button>
    </view>
    <button @click="redeemCode" :disabled="!isValidCode" class="redeem-btn">
      立即兑换
    </button>
  </view>
</template>

<script>
export default {
  data() {
    return {
      codeValue: ''
    }
  },
  computed: {
    isValidCode() {
      return /^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/.test(this.codeValue)
    }
  },
  methods: {
    formatCode() {
      // 自动添加连字符
      let value = this.codeValue.replace(/[^A-Z0-9]/g, '').toUpperCase()
      if (value.length > 16) value = value.slice(0, 16)

      let formatted = ''
      for (let i = 0; i < value.length; i += 4) {
        if (i > 0) formatted += '-'
        formatted += value.slice(i, i + 4)
      }
      this.codeValue = formatted
    },

    async scanCode() {
      try {
        const res = await uni.scanCode({
          scanType: ['qrCode', 'barCode']
        })
        this.codeValue = res.result
        this.formatCode()
      } catch (e) {
        uni.showToast({ title: '扫码失败', icon: 'none' })
      }
    },

    async redeemCode() {
      if (!this.isValidCode) return

      try {
        uni.showLoading({ title: '兑换中...' })
        const result = await this.$request.post('/wowpic/gift/redeem', {
          code: this.codeValue
        })

        uni.hideLoading()
        this.$emit('redeem-success', result)

      } catch (e) {
        uni.hideLoading()
        uni.showToast({ title: e.message || '兑换失败', icon: 'none' })
      }
    }
  }
}
</script>
```

### 9.5 部署配置

#### 9.5.1 环境变量配置
```bash
# .env 文件新增配置
# Web端配置
WEB_DOMAIN=https://web.wowpic.com
WEB_JWT_SECRET=your-web-jwt-secret

# 短信服务配置（如果使用手机号登录）
SMS_ACCESS_KEY=your-sms-access-key
SMS_SECRET_KEY=your-sms-secret-key
SMS_TEMPLATE_ID=your-template-id

# 支付宝配置（Web端支付）
ALIPAY_APP_ID=your-alipay-app-id
ALIPAY_PRIVATE_KEY_PATH=./certs/alipay_private_key.pem
ALIPAY_PUBLIC_KEY_PATH=./certs/alipay_public_key.pem

# Redis配置（用于二维码登录）
REDIS_URL=redis://localhost:6379/1
```

#### 9.5.2 Nginx配置示例
```nginx
# Web端配置
server {
    listen 443 ssl;
    server_name web.wowpic.com;

    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;

    location / {
        root /var/www/wowpic-web/dist;
        try_files $uri $uri/ /index.html;
    }

    location /api/ {
        proxy_pass http://localhost:8000/wowpic/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 10. 总结

这个开发计划详细涵盖了：

1. **完整的数据库设计** - 支持兑换码、分享奖励、Web端会话
2. **详细的API接口规范** - 包含请求响应格式
3. **核心业务逻辑实现** - 兑换码生成、兑换、Web端认证
4. **前端开发指导** - 小程序页面和Web端技术栈
5. **部署配置说明** - 环境变量和服务器配置

该计划充分利用了现有项目架构的优势，最小化了对现有代码的影响，同时为未来扩展提供了良好的基础。每个阶段都有明确的交付物和验收标准，便于项目管理和质量控制。
